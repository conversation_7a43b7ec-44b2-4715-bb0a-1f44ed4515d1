import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/shared/types'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      )
    }

    supabase.auth.setAuth(authHeader.replace('Bearer ', ''))

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      )
    }

    // Get user's comparison sessions
    const { data: comparisons, error } = await supabase
      .from('comparison_sessions')
      .select(`
        id,
        name,
        ev_model_ids,
        user_priorities,
        comparison_notes,
        is_public,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false })

    if (error) {
      console.error('Error fetching comparisons:', error)
      return NextResponse.json(
        { error: 'Failed to fetch comparisons' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      data: comparisons || []
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      )
    }

    supabase.auth.setAuth(authHeader.replace('Bearer ', ''))

    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, ev_model_ids, user_priorities, comparison_notes, is_public } = body

    if (!ev_model_ids || !Array.isArray(ev_model_ids) || ev_model_ids.length === 0) {
      return NextResponse.json(
        { error: 'At least one EV model ID is required' },
        { status: 400 }
      )
    }

    if (ev_model_ids.length > 4) {
      return NextResponse.json(
        { error: 'Maximum 4 EV models can be compared' },
        { status: 400 }
      )
    }

    // Verify all EV models exist
    const { data: evModels, error: modelsError } = await supabase
      .from('ev_models')
      .select('id')
      .in('id', ev_model_ids)

    if (modelsError || !evModels || evModels.length !== ev_model_ids.length) {
      return NextResponse.json(
        { error: 'One or more EV models not found' },
        { status: 404 }
      )
    }

    // Create comparison session
    const { data: comparison, error } = await supabase
      .from('comparison_sessions')
      .insert({
        user_id: user.id,
        name: name || `Comparison ${new Date().toLocaleDateString()}`,
        ev_model_ids,
        user_priorities: user_priorities || null,
        comparison_notes: comparison_notes || null,
        is_public: is_public || false
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating comparison:', error)
      return NextResponse.json(
        { error: 'Failed to create comparison' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      data: comparison,
      message: 'Comparison created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
