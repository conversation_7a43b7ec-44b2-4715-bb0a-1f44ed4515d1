'use client'

import { useState } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ComparisonButton } from '@/components/comparison/ComparisonButton'
import {
  Heart,
  Plus,
  Share2,
  Star,
  Zap,
  Battery,
  Clock,
  DollarSign,
  ChevronLeft,
  ChevronRight,
  ExternalLink,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  formatPrice,
  formatRange,
  formatChargingTime,
  formatAcceleration,
} from '@/shared/utils/ev-buyer-guide'
import { PLACEHOLDER_IMAGES } from '@/shared/constants/ev-buyer-guide'
import type { EVModelWithDetails } from '@/shared/types'

interface EVModelHeroProps {
  evModel: EVModelWithDetails
  onAddToFavorites?: () => void
  onShare?: () => void
  isFavorite?: boolean
}

export function EVModelHero({
  evModel,
  onAddToFavorites,
  onAddToComparison,
  onShare,
  isFavorite = false,
  isInComparison = false,
}: EVModelHeroProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [imageError, setImageError] = useState(false)

  // Use placeholder images if no images are available
  const images =
    evModel.images && evModel.images.length > 0 ? evModel.images : [PLACEHOLDER_IMAGES.ev_model]

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length)
  }

  const manufacturerLogo =
    evModel.ev_manufacturers?.logo_url || PLACEHOLDER_IMAGES.manufacturer_logo

  return (
    <section className="bg-white py-8 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Image Gallery */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="relative aspect-video overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-800">
              <Image
                src={imageError ? PLACEHOLDER_IMAGES.ev_model : images[currentImageIndex]}
                alt={`${evModel.make} ${evModel.model}`}
                fill
                className="object-cover"
                onError={() => setImageError(true)}
                priority
              />

              {/* Image Navigation */}
              {images.length > 1 && (
                <>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 h-10 w-10 -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 h-10 w-10 -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* Image Indicators */}
              {images.length > 1 && (
                <div className="absolute bottom-4 left-1/2 flex -translate-x-1/2 space-x-2">
                  {images.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={cn(
                        'h-2 w-2 rounded-full transition-colors',
                        index === currentImageIndex ? 'bg-white' : 'bg-white/50 hover:bg-white/75'
                      )}
                    />
                  ))}
                </div>
              )}

              {/* Badges */}
              <div className="absolute left-4 top-4 flex flex-col gap-2">
                {evModel.is_featured && (
                  <Badge className="bg-electric-600 text-white">
                    <Star className="mr-1 h-3 w-3" />
                    Featured
                  </Badge>
                )}
                {evModel.best_value && <Badge variant="secondary">Best Value</Badge>}
                {evModel.editor_choice && (
                  <Badge className="bg-amber-600 text-white">Editor's Choice</Badge>
                )}
              </div>
            </div>

            {/* Thumbnail Gallery */}
            {images.length > 1 && (
              <div className="flex space-x-2 overflow-x-auto">
                {images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={cn(
                      'relative h-16 w-24 shrink-0 overflow-hidden rounded border-2 transition-colors',
                      index === currentImageIndex
                        ? 'border-electric-600'
                        : 'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600'
                    )}
                  >
                    <Image
                      src={image}
                      alt={`${evModel.make} ${evModel.model} view ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Details */}
          <div className="space-y-6">
            {/* Header */}
            <div>
              <div className="mb-2 flex items-center gap-3">
                {manufacturerLogo && (
                  <Image
                    src={manufacturerLogo}
                    alt={evModel.make}
                    width={32}
                    height={32}
                    className="rounded"
                  />
                )}
                <span className="text-lg text-gray-600 dark:text-gray-400">{evModel.make}</span>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 lg:text-4xl">
                {evModel.model}
              </h1>

              {evModel.trim && (
                <p className="text-xl text-gray-600 dark:text-gray-400">{evModel.trim}</p>
              )}

              <div className="mt-2 flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                <span>{evModel.year}</span>
                <span>•</span>
                <span className="capitalize">{evModel.body_type}</span>
                <span>•</span>
                <span className="capitalize">{evModel.drivetrain}</span>
              </div>
            </div>

            {/* Pricing */}
            <div className="space-y-2">
              <div className="text-3xl font-bold text-electric-600">
                {formatPrice(evModel.price_msrp)}
              </div>
              {evModel.price_base && evModel.price_base !== evModel.price_msrp && (
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Starting from {formatPrice(evModel.price_base)}
                </div>
              )}
              {evModel.buyerMetrics.costPerMile && (
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  ${evModel.buyerMetrics.costPerMile} per mile of range
                </div>
              )}
            </div>

            {/* Key Specs */}
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="flex items-center gap-3 p-4">
                  <Battery className="h-5 w-5 text-electric-600" />
                  <div>
                    <div className="font-semibold">{formatRange(evModel.range_epa_miles)}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">EPA Range</div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="flex items-center gap-3 p-4">
                  <Zap className="h-5 w-5 text-electric-600" />
                  <div>
                    <div className="font-semibold">
                      {evModel.charging_speed_dc_kw ? `${evModel.charging_speed_dc_kw}kW` : 'N/A'}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">DC Charging</div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="flex items-center gap-3 p-4">
                  <Clock className="h-5 w-5 text-electric-600" />
                  <div>
                    <div className="font-semibold">
                      {formatChargingTime(evModel.charging_time_10_80_minutes)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">10-80% Charge</div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="flex items-center gap-3 p-4">
                  <div className="h-5 w-5 text-electric-600">⚡</div>
                  <div>
                    <div className="font-semibold">
                      {formatAcceleration(evModel.acceleration_0_60_mph)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">0-60 mph</div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <Button size="lg" className="min-w-[140px] flex-1">
                <ExternalLink className="mr-2 h-4 w-4" />
                View on {evModel.make} Website
              </Button>

              <Button
                variant="outline"
                size="lg"
                onClick={onAddToFavorites}
                className={cn(
                  'transition-colors',
                  isFavorite &&
                    'bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-950 dark:text-red-400'
                )}
              >
                <Heart className={cn('mr-2 h-4 w-4', isFavorite && 'fill-current')} />
                {isFavorite ? 'Saved' : 'Save'}
              </Button>

              <Button
                variant="outline"
                size="lg"
                onClick={onAddToComparison}
                disabled={isInComparison}
                className={cn(
                  'transition-colors',
                  isInComparison && 'dark:bg-electric-950 bg-electric-50 text-electric-600'
                )}
              >
                <Plus className="mr-2 h-4 w-4" />
                {isInComparison ? 'Added' : 'Compare'}
              </Button>

              <Button variant="outline" size="lg" onClick={onShare}>
                <Share2 className="mr-2 h-4 w-4" />
                Share
              </Button>
            </div>

            {/* Quick Facts */}
            {evModel.buyerMetrics.practicalRange && (
              <div className="dark:bg-electric-950 rounded-lg bg-electric-50 p-4">
                <h3 className="mb-2 font-semibold text-electric-900 dark:text-electric-100">
                  Real-World Insight
                </h3>
                <p className="text-sm text-electric-800 dark:text-electric-200">
                  Expect around <strong>{evModel.buyerMetrics.practicalRange} miles</strong> of
                  real-world range in typical driving conditions.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}
