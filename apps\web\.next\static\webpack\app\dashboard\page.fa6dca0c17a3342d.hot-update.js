"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/git-compare.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/lucide-react/dist/esm/icons/git-compare.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GitCompare; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst GitCompare = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"GitCompare\", [\n  [\"circle\", { cx: \"18\", cy: \"18\", r: \"3\", key: \"1xkwt0\" }],\n  [\"circle\", { cx: \"6\", cy: \"6\", r: \"3\", key: \"1lh9wr\" }],\n  [\"path\", { d: \"M13 6h3a2 2 0 0 1 2 2v7\", key: \"1yeb86\" }],\n  [\"path\", { d: \"M11 18H8a2 2 0 0 1-2-2V9\", key: \"19pyzm\" }]\n]);\n\n\n//# sourceMappingURL=git-compare.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2dpdC1jb21wYXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQsbUJBQW1CLGdFQUFnQjtBQUNuQyxlQUFlLDJDQUEyQztBQUMxRCxlQUFlLHlDQUF5QztBQUN4RCxhQUFhLDZDQUE2QztBQUMxRCxhQUFhLDhDQUE4QztBQUMzRDs7QUFFaUM7QUFDakMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZ2l0LWNvbXBhcmUuanM/YWJhMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4yOTQuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IEdpdENvbXBhcmUgPSBjcmVhdGVMdWNpZGVJY29uKFwiR2l0Q29tcGFyZVwiLCBbXG4gIFtcImNpcmNsZVwiLCB7IGN4OiBcIjE4XCIsIGN5OiBcIjE4XCIsIHI6IFwiM1wiLCBrZXk6IFwiMXhrd3QwXCIgfV0sXG4gIFtcImNpcmNsZVwiLCB7IGN4OiBcIjZcIiwgY3k6IFwiNlwiLCByOiBcIjNcIiwga2V5OiBcIjFsaDl3clwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMTMgNmgzYTIgMiAwIDAgMSAyIDJ2N1wiLCBrZXk6IFwiMXllYjg2XCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xMSAxOEg4YTIgMiAwIDAgMS0yLTJWOVwiLCBrZXk6IFwiMTlweXptXCIgfV1cbl0pO1xuXG5leHBvcnQgeyBHaXRDb21wYXJlIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdpdC1jb21wYXJlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/git-compare.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/comparison/ComparisonToggle.tsx":
/*!********************************************************!*\
  !*** ./src/components/comparison/ComparisonToggle.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComparisonToggle: function() { return /* binding */ ComparisonToggle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_GitCompare_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=GitCompare!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/git-compare.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ComparisonContext */ \"(app-pages-browser)/./src/contexts/ComparisonContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ ComparisonToggle auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ComparisonToggle(param) {\n    let { className } = param;\n    _s();\n    const { state, togglePanel } = (0,_contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_4__.useComparison)();\n    if (state.models.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n        variant: state.isOpen ? \"default\" : \"outline\",\n        onClick: togglePanel,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative transition-colors\", state.isOpen && \"bg-electric-600 text-white hover:bg-electric-700\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GitCompare_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonToggle.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            \"Compare\",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                variant: state.isOpen ? \"secondary\" : \"default\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"ml-2 h-5 w-5 rounded-full p-0 text-xs\", state.isOpen && \"bg-white text-electric-600\"),\n                children: state.models.length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonToggle.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonToggle.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(ComparisonToggle, \"ms7d95nhrTquxY89QuzAxp1j4oo=\", false, function() {\n    return [\n        _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_4__.useComparison\n    ];\n});\n_c = ComparisonToggle;\nvar _c;\n$RefreshReg$(_c, \"ComparisonToggle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/comparison/ComparisonToggle.tsx\n"));

/***/ })

});