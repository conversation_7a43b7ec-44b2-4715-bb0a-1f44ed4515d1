/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/signin/page";
exports.ids = ["app/auth/signin/page"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!****************************************************************!*\
  !*** ../../node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \****************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fsignin%2Fpage&page=%2Fauth%2Fsignin%2Fpage&appPaths=%2Fauth%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fsignin%2Fpage&page=%2Fauth%2Fsignin%2Fpage&appPaths=%2Fauth%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?64b7\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'signin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/signin/page.tsx */ \"(rsc)/./src/app/auth/signin/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/signin/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/signin/page\",\n        pathname: \"/auth/signin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fsignin%2Fpage&page=%2Fauth%2Fsignin%2Fpage&appPaths=%2Fauth%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cauth%5Csignin%5Cpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cauth%5Csignin%5Cpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/signin/page.tsx */ \"(ssr)/./src/app/auth/signin/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q29iaV9jJTVDRGVza3RvcCU1Q2V2LWFwcCU1Q2FwcHMlNUN3ZWIlNUNzcmMlNUNhcHAlNUNhdXRoJTVDc2lnbmluJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGdyZWVubWlsZXMtZXYvd2ViLz80MTlmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcb2JpX2NcXFxcRGVza3RvcFxcXFxldi1hcHBcXFxcYXBwc1xcXFx3ZWJcXFxcc3JjXFxcXGFwcFxcXFxhdXRoXFxcXHNpZ25pblxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cauth%5Csignin%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CThemeProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CToastProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CThemeProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CToastProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AuthErrorBoundary.tsx */ \"(ssr)/./src/components/AuthErrorBoundary.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ToastProvider.tsx */ \"(ssr)/./src/components/ToastProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CThemeProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CToastProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/signin/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signin/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_AuthErrorBoundary__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AuthErrorBoundary */ \"(ssr)/./src/components/AuthErrorBoundary.tsx\");\n/* harmony import */ var _components_AuthDebugger__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/AuthDebugger */ \"(ssr)/./src/components/AuthDebugger.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction SignInPage() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { signIn, signInWithGoogle, signInWithGitHub } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const { handleAuthError } = (0,_components_AuthErrorBoundary__WEBPACK_IMPORTED_MODULE_10__.useAuthErrorHandler)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setLoading(true);\n        try {\n            console.log(\"Starting sign in process...\");\n            // Add timeout to prevent hanging\n            const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Sign in timeout - please try again\")), 15000));\n            const signInPromise = signIn(email, password);\n            const { error } = await Promise.race([\n                signInPromise,\n                timeoutPromise\n            ]);\n            if (error) {\n                console.error(\"Sign in failed:\", error);\n                setError(handleAuthError(error));\n                setLoading(false);\n            } else {\n                console.log(\"Sign in successful, redirecting...\");\n                // Don't set loading false here - let the auth context handle it\n                // Add a small delay to ensure auth state is updated\n                setTimeout(()=>{\n                    router.push(\"/dashboard\");\n                }, 500);\n            }\n        } catch (err) {\n            console.error(\"Sign in error:\", err);\n            setError(err.message || \"An unexpected error occurred\");\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen items-center justify-center bg-gradient-to-br from-electric-50 to-green-50 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-8 w-8 text-electric-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"GreenMilesEV\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"mb-2 text-2xl font-bold text-gray-900\",\n                                children: \"Welcome back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Sign in to your account to continue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: \"Enter your email and password to access your account\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-4\",\n                                        children: [\n                                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                                variant: \"destructive\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"email\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"email\",\n                                                        type: \"email\",\n                                                        placeholder: \"Enter your email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        required: true,\n                                                        disabled: loading\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"password\",\n                                                        children: \"Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                id: \"password\",\n                                                                type: showPassword ? \"text\" : \"password\",\n                                                                placeholder: \"Enter your password\",\n                                                                value: password,\n                                                                onChange: (e)=>setPassword(e.target.value),\n                                                                required: true,\n                                                                disabled: loading\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 104,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                onClick: ()=>setShowPassword(!showPassword),\n                                                                disabled: loading,\n                                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 121,\n                                                                    columnNumber: 37\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 121,\n                                                                    columnNumber: 70\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/auth/forgot-password\",\n                                                    className: \"text-sm text-electric-600 hover:text-electric-700 hover:underline\",\n                                                    children: \"Forgot password?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"submit\",\n                                                className: \"w-full bg-electric-600 hover:bg-electric-700\",\n                                                disabled: loading,\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Signing in...\"\n                                                    ]\n                                                }, void 0, true) : \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 flex items-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-full border-t\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex justify-center text-xs uppercase\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-background px-2 text-muted-foreground\",\n                                                            children: \"Or continue with\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        disabled: loading,\n                                                        onClick: signInWithGoogle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"mr-2 h-4 w-4\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\",\n                                                                        fill: \"#4285F4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                        lineNumber: 164,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\",\n                                                                        fill: \"#34A853\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                        lineNumber: 168,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\",\n                                                                        fill: \"#FBBC05\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\",\n                                                                        fill: \"#EA4335\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                        lineNumber: 176,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Google\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        disabled: loading,\n                                                        onClick: signInWithGitHub,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"mr-2 h-4 w-4\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"GitHub\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 text-center text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Don't have an account? \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/auth/signup\",\n                                                className: \"font-medium text-electric-600 hover:text-electric-700 hover:underline\",\n                                                children: \"Sign up\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthDebugger__WEBPACK_IMPORTED_MODULE_11__.AuthDebugger, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/signin/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthDebugger.tsx":
/*!*****************************************!*\
  !*** ./src/components/AuthDebugger.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthDebugger: () => (/* binding */ AuthDebugger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ AuthDebugger auto */ \n\n\nfunction AuthDebugger() {\n    const { user, session, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const addLog = (message)=>{\n            const timestamp = new Date().toLocaleTimeString();\n            setLogs((prev)=>[\n                    ...prev.slice(-9),\n                    `${timestamp}: ${message}`\n                ]);\n        };\n        addLog(`Auth state - Loading: ${loading}, User: ${user?.email || \"null\"}, Session: ${session ? \"exists\" : \"null\"}`);\n    }, [\n        user,\n        session,\n        loading\n    ]);\n    // Only show in development\n    if (false) {}\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 max-w-sm rounded-lg bg-black/80 p-4 text-xs text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-2 font-bold\",\n                children: \"Auth Debug\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthDebugger.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \"Loading: \",\n                            loading ? \"true\" : \"false\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthDebugger.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \"User: \",\n                            user?.email || \"null\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthDebugger.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \"Session: \",\n                            session ? \"exists\" : \"null\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthDebugger.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthDebugger.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 border-t border-gray-600 pt-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-1 text-xs font-semibold\",\n                        children: \"Recent logs:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthDebugger.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-32 overflow-y-auto text-xs\",\n                        children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-300\",\n                                children: log\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthDebugger.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthDebugger.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthDebugger.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthDebugger.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthDebugger.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthErrorBoundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/AuthErrorBoundary.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthErrorBoundary: () => (/* binding */ AuthErrorBoundary),\n/* harmony export */   useAuthErrorHandler: () => (/* binding */ useAuthErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ AuthErrorBoundary,useAuthErrorHandler auto */ \n\n\n\n\nclass AuthErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.retry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"Authentication error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                error: this.state.error,\n                retry: this.retry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                lineNumber: 42,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, retry }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.AlertTitle, {\n                            children: \"Authentication Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.AlertDescription, {\n                            className: \"mt-2\",\n                            children: error.message || \"An unexpected error occurred during authentication.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: retry,\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>window.location.href = \"/\",\n                            className: \"flex-1\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n// Hook for handling auth errors in functional components\nfunction useAuthErrorHandler() {\n    const handleAuthError = (error)=>{\n        console.error(\"Auth error:\", error);\n        // Handle specific error types\n        if (error?.message?.includes(\"Invalid login credentials\")) {\n            return \"Invalid email or password. Please check your credentials and try again.\";\n        }\n        if (error?.message?.includes(\"Email not confirmed\")) {\n            return \"Please check your email and click the confirmation link before signing in.\";\n        }\n        if (error?.message?.includes(\"Too many requests\")) {\n            return \"Too many login attempts. Please wait a few minutes before trying again.\";\n        }\n        if (error?.message?.includes(\"Network\")) {\n            return \"Network error. Please check your internet connection and try again.\";\n        }\n        // Default error message\n        return error?.message || \"An unexpected error occurred. Please try again.\";\n    };\n    return {\n        handleAuthError\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL0BncmVlbm1pbGVzLWV2L3dlYi8uL3NyYy9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4PzZmNDciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIgfSBmcm9tICduZXh0LXRoZW1lcydcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSAnbmV4dC10aGVtZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ToastProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ToastProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider auto */ \n\nfunction ToastProvider() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        position: \"top-right\",\n        toastOptions: {\n            // Default options\n            duration: 4000,\n            style: {\n                background: \"var(--background)\",\n                color: \"var(--foreground)\",\n                border: \"1px solid var(--border)\"\n            },\n            // Success toast styling\n            success: {\n                duration: 3000,\n                style: {\n                    background: \"rgb(34 197 94)\",\n                    color: \"white\",\n                    border: \"1px solid rgb(34 197 94)\"\n                },\n                iconTheme: {\n                    primary: \"white\",\n                    secondary: \"rgb(34 197 94)\"\n                }\n            },\n            // Error toast styling\n            error: {\n                duration: 5000,\n                style: {\n                    background: \"rgb(239 68 68)\",\n                    color: \"white\",\n                    border: \"1px solid rgb(239 68 68)\"\n                },\n                iconTheme: {\n                    primary: \"white\",\n                    secondary: \"rgb(239 68 68)\"\n                }\n            },\n            // Loading toast styling\n            loading: {\n                style: {\n                    background: \"rgb(59 130 246)\",\n                    color: \"white\",\n                    border: \"1px solid rgb(59 130 246)\"\n                },\n                iconTheme: {\n                    primary: \"white\",\n                    secondary: \"rgb(59 130 246)\"\n                }\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ToastProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ToastProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL0BncmVlbm1pbGVzLWV2L3dlYi8uL3NyYy9jb21wb25lbnRzL3VpL2lucHV0LnRzeD9jOTgzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/../../node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ3lCO0FBQ1U7QUFFakM7QUFFaEMsTUFBTUksZ0JBQWdCRiw2REFBR0EsQ0FDdkI7QUFHRixNQUFNRyxzQkFBUUwsNkNBQWdCLENBSTVCLENBQUMsRUFBRU8sU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDUix1REFBbUI7UUFDbEJRLEtBQUtBO1FBQ0xGLFdBQVdKLDhDQUFFQSxDQUFDQyxpQkFBaUJHO1FBQzlCLEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxNQUFNTSxXQUFXLEdBQUdWLHVEQUFtQixDQUFDVSxXQUFXO0FBRW5DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGdyZWVubWlsZXMtZXYvd2ViLy4vc3JjL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4PzEzZWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtbGFiZWxcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGxhYmVsVmFyaWFudHMgPSBjdmEoXG4gIFwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwXCJcbilcblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PiAmXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBsYWJlbFZhcmlhbnRzPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8TGFiZWxQcmltaXRpdmUuUm9vdFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24obGFiZWxWYXJpYW50cygpLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5MYWJlbC5kaXNwbGF5TmFtZSA9IExhYmVsUHJpbWl0aXZlLlJvb3QuZGlzcGxheU5hbWVcblxuZXhwb3J0IHsgTGFiZWwgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjdmEiLCJjbiIsImxhYmVsVmFyaWFudHMiLCJMYWJlbCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsIlJvb3QiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n            if (error) {\n                console.error(\"Error getting session:\", error);\n            }\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchProfile();\n            }\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state changed:\", event, session?.user?.email);\n            setSession(session);\n            setUser(session?.user ?? null);\n            try {\n                if (session?.user) {\n                    await fetchProfile();\n                } else {\n                    setProfile(null);\n                }\n            } catch (error) {\n                console.error(\"Error in auth state change handler:\", error);\n            } finally{\n                // Always set loading to false after handling auth state change\n                setLoading(false);\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const fetchProfile = async ()=>{\n        try {\n            console.log(\"Fetching user profile...\");\n            // Add timeout to prevent hanging\n            const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Profile fetch timeout\")), 10000));\n            const profilePromise = _lib_api__WEBPACK_IMPORTED_MODULE_3__.profileApi.getProfile();\n            const { data, error } = await Promise.race([\n                profilePromise,\n                timeoutPromise\n            ]);\n            if (error) {\n                console.error(\"Error fetching profile:\", error);\n            // Don't throw error - just log it and continue\n            } else {\n                console.log(\"Profile fetched successfully:\", data);\n                setProfile(data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n        // Don't throw error - just log it and continue\n        // This prevents the auth flow from hanging if profile fetch fails\n        }\n    };\n    const signUp = async (email, password, metadata)=>{\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: metadata\n                }\n            });\n            return {\n                data,\n                error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            console.log(\"Attempting sign in for:\", email);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                console.error(\"Sign in error:\", error);\n                setLoading(false) // Only set loading false on error\n                ;\n            } else {\n                console.log(\"Sign in successful, waiting for auth state change...\");\n            // Don't set loading false here - let the auth state change handler do it\n            }\n            return {\n                data,\n                error\n            };\n        } catch (err) {\n            console.error(\"Sign in exception:\", err);\n            setLoading(false);\n            throw err;\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            return {\n                error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetPassword = async (email)=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/reset-password`\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const signInWithGoogle = async ()=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n            provider: \"google\",\n            options: {\n                redirectTo: `${window.location.origin}/dashboard`\n            }\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const signInWithGitHub = async ()=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n            provider: \"github\",\n            options: {\n                redirectTo: `${window.location.origin}/dashboard`\n            }\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const updateProfile = async (updates)=>{\n        try {\n            const { data, error } = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.profileApi.updateProfile(updates);\n            if (data && !error) {\n                setProfile(data);\n            }\n            return {\n                data,\n                error\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    };\n    const refreshProfile = async ()=>{\n        await fetchProfile();\n    };\n    const value = {\n        user,\n        profile,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        signInWithGoogle,\n        signInWithGitHub,\n        updateProfile,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 224,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Higher-order component for protected routes\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { user, loading } = useAuth();\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex min-h-screen items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 w-32 animate-spin rounded-full border-b-2 border-electric-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            // Redirect to login page\n            window.location.href = \"/auth/signin\";\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 254,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsApi: () => (/* binding */ analyticsApi),\n/* harmony export */   chargingSessionApi: () => (/* binding */ chargingSessionApi),\n/* harmony export */   chargingStationApi: () => (/* binding */ chargingStationApi),\n/* harmony export */   maintenanceApi: () => (/* binding */ maintenanceApi),\n/* harmony export */   profileApi: () => (/* binding */ profileApi),\n/* harmony export */   tripApi: () => (/* binding */ tripApi),\n/* harmony export */   vehicleApi: () => (/* binding */ vehicleApi)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\n// Profile API\nconst profileApi = {\n    async getProfile () {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            return {\n                data: null,\n                error: {\n                    message: \"No authenticated user\"\n                }\n            };\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateProfile (updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", (await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser()).data.user?.id).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async uploadAvatar (file) {\n        try {\n            const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (!user) throw new Error(\"No user found\");\n            const fileExt = file.name.split(\".\").pop();\n            const fileName = `${user.id}-${Math.random()}.${fileExt}`;\n            const filePath = `avatars/${fileName}`;\n            // Upload file to storage\n            const { data: uploadData, error: uploadError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(\"avatars\").upload(filePath, file, {\n                cacheControl: \"3600\",\n                upsert: false\n            });\n            if (uploadError) throw uploadError;\n            // Get public URL\n            const { data: { publicUrl } } = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(\"avatars\").getPublicUrl(filePath);\n            // Update profile with new avatar URL\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").update({\n                avatar_url: publicUrl,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            return {\n                data,\n                error\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    },\n    async deleteAvatar () {\n        try {\n            const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (!user) throw new Error(\"No user found\");\n            // Get current profile to find avatar URL\n            const { data: profile } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").select(\"avatar_url\").eq(\"id\", user.id).single();\n            // Delete file from storage if it exists\n            if (profile?.avatar_url) {\n                const fileName = profile.avatar_url.split(\"/\").pop();\n                if (fileName) {\n                    await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(\"avatars\").remove([\n                        `avatars/${fileName}`\n                    ]);\n                }\n            }\n            // Update profile to remove avatar URL\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").update({\n                avatar_url: null,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            return {\n                data,\n                error\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    },\n    async getUserStats () {\n        try {\n            const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (!user) throw new Error(\"No user found\");\n            // Get vehicle count\n            const { count: vehicleCount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"user_id\", user.id);\n            // Get charging session count\n            const { count: chargingSessionCount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"user_id\", user.id);\n            // Get total miles from trips\n            const { data: tripsData } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(\"distance_miles\").eq(\"user_id\", user.id);\n            const totalMiles = tripsData?.reduce((sum, trip)=>sum + (trip.distance_miles || 0), 0) || 0;\n            return {\n                data: {\n                    vehicleCount: vehicleCount || 0,\n                    chargingSessionCount: chargingSessionCount || 0,\n                    totalMiles: Math.round(totalMiles)\n                },\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: {\n                    vehicleCount: 0,\n                    chargingSessionCount: 0,\n                    totalMiles: 0\n                },\n                error\n            };\n        }\n    }\n};\n// Vehicle API\nconst vehicleApi = {\n    async getVehicles () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        return {\n            data,\n            error\n        };\n    },\n    async getVehicle (id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\").eq(\"id\", id).single();\n        return {\n            data,\n            error\n        };\n    },\n    async createVehicle (vehicle) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").insert([\n            vehicle\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateVehicle (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async deleteVehicle (id) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").delete().eq(\"id\", id);\n        return {\n            error\n        };\n    }\n};\n// Charging Station API\nconst chargingStationApi = {\n    async getChargingStations (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_stations\").select(\"*\").eq(\"status\", \"operational\").limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async getNearbyStations (latitude, longitude, radiusMiles = 25) {\n        // Using PostGIS extension for geographic queries (if available)\n        // For now, we'll fetch all stations and filter client-side\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_stations\").select(\"*\").eq(\"status\", \"operational\");\n        if (data && !error) {\n            // Simple distance calculation (for production, use PostGIS)\n            const stationsWithDistance = data.map((station)=>({\n                    ...station,\n                    distance: calculateDistance(latitude, longitude, station.latitude, station.longitude)\n                })).filter((station)=>station.distance <= radiusMiles).sort((a, b)=>a.distance - b.distance);\n            return {\n                data: stationsWithDistance,\n                error\n            };\n        }\n        return {\n            data,\n            error\n        };\n    }\n};\n// Charging Session API\nconst chargingSessionApi = {\n    async getChargingSessions (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(`\n        *,\n        vehicle:vehicles(make, model, year),\n        charging_station:charging_stations(name, network, address)\n      `).order(\"start_time\", {\n            ascending: false\n        }).limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async createChargingSession (session) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").insert([\n            session\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateChargingSession (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Trip API\nconst tripApi = {\n    async getTrips (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(`\n        *,\n        vehicle:vehicles(make, model, year)\n      `).order(\"start_time\", {\n            ascending: false\n        }).limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async createTrip (trip) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").insert([\n            trip\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateTrip (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Maintenance API\nconst maintenanceApi = {\n    async getMaintenanceRecords (vehicleId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").select(`\n        *,\n        vehicle:vehicles(make, model, year)\n      `).order(\"service_date\", {\n            ascending: false\n        });\n        if (vehicleId) {\n            query = query.eq(\"vehicle_id\", vehicleId);\n        }\n        const { data, error } = await query;\n        return {\n            data,\n            error\n        };\n    },\n    async createMaintenanceRecord (record) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").insert([\n            record\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateMaintenanceRecord (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Analytics API\nconst analyticsApi = {\n    async getVehicleAnalytics (vehicleId, startDate, endDate) {\n        // Get trips for the vehicle\n        let tripsQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(\"*\").eq(\"vehicle_id\", vehicleId).eq(\"status\", \"completed\");\n        if (startDate) tripsQuery = tripsQuery.gte(\"start_time\", startDate);\n        if (endDate) tripsQuery = tripsQuery.lte(\"start_time\", endDate);\n        const { data: trips, error: tripsError } = await tripsQuery;\n        // Get charging sessions for the vehicle\n        let sessionsQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(\"*\").eq(\"vehicle_id\", vehicleId).eq(\"status\", \"completed\");\n        if (startDate) sessionsQuery = sessionsQuery.gte(\"start_time\", startDate);\n        if (endDate) sessionsQuery = sessionsQuery.lte(\"start_time\", endDate);\n        const { data: sessions, error: sessionsError } = await sessionsQuery;\n        if (tripsError || sessionsError) {\n            return {\n                data: null,\n                error: tripsError || sessionsError\n            };\n        }\n        // Calculate analytics\n        const totalMiles = trips?.reduce((sum, trip)=>sum + (trip.distance_miles || 0), 0) || 0;\n        const totalTrips = trips?.length || 0;\n        const totalEnergyUsed = trips?.reduce((sum, trip)=>sum + (trip.energy_used_kwh || 0), 0) || 0;\n        const totalChargingCost = sessions?.reduce((sum, session)=>sum + (session.cost_total || 0), 0) || 0;\n        const averageEfficiency = totalEnergyUsed > 0 ? totalMiles / totalEnergyUsed : 0;\n        // Estimate carbon and money savings (compared to average gas car)\n        const carbonSavedLbs = totalMiles * 0.89 // Rough estimate: 0.89 lbs CO2 per mile for gas car\n        ;\n        const moneySaved = totalMiles / 25 * 3.5 - totalChargingCost // Assume 25 MPG, $3.50/gallon\n        ;\n        const analytics = {\n            total_miles: Math.round(totalMiles * 10) / 10,\n            total_trips: totalTrips,\n            average_efficiency: Math.round(averageEfficiency * 100) / 100,\n            total_energy_used: Math.round(totalEnergyUsed * 10) / 10,\n            total_charging_cost: Math.round(totalChargingCost * 100) / 100,\n            carbon_saved_lbs: Math.round(carbonSavedLbs * 10) / 10,\n            money_saved: Math.round(moneySaved * 100) / 100\n        };\n        return {\n            data: analytics,\n            error: null\n        };\n    }\n};\n// Helper function for distance calculation\nfunction calculateDistance(lat1, lon1, lat2, lon2) {\n    const R = 3959 // Earth's radius in miles\n    ;\n    const dLat = toRadians(lat2 - lat1);\n    const dLon = toRadians(lon2 - lon1);\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n}\nfunction toRadians(degrees) {\n    return degrees * (Math.PI / 180);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://pbevpexclffmhqstwlha.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZXZwZXhjbGZmbWhxc3R3bGhhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjA1MzksImV4cCI6MjA2NTQ5NjUzOX0.F6bMKoeKV5QTyt0oEfNDn6-s780wkvvddplEBUuRpsI\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBR3BELE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUVqRSxJQUFJLENBQUNELGVBQWUsQ0FBQ0ksaUJBQWlCO0lBQ3BDLE1BQU0sSUFBSUUsTUFBTTtBQUNsQjtBQUVPLE1BQU1DLFdBQVdSLG1FQUFZQSxDQUFXQyxhQUFhSSxpQkFBaUI7SUFDM0VJLE1BQU07UUFDSkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLG9CQUFvQjtJQUN0QjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZ3JlZW5taWxlcy1ldi93ZWIvLi9zcmMvbGliL3N1cGFiYXNlLnRzPzA2ZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuaW1wb3J0IHR5cGUgeyBEYXRhYmFzZSB9IGZyb20gJ0Avc2hhcmVkL3R5cGVzJ1xuXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCFcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuXG5pZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUFub25LZXkpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIFN1cGFiYXNlIGVudmlyb25tZW50IHZhcmlhYmxlcycpXG59XG5cbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudDxEYXRhYmFzZT4oc3VwYWJhc2VVcmwsIHN1cGFiYXNlQW5vbktleSwge1xuICBhdXRoOiB7XG4gICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSxcbiAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcbiAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUsXG4gIH0sXG59KVxuXG4vLyBFeHBvcnQgdHlwZXMgZm9yIGNvbnZlbmllbmNlXG5leHBvcnQgdHlwZSB7IERhdGFiYXNlIH0gZnJvbSAnQC9zaGFyZWQvdHlwZXMnXG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJFcnJvciIsInN1cGFiYXNlIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiIsImRldGVjdFNlc3Npb25JblVybCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BncmVlbm1pbGVzLWV2L3dlYi8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"067048cfb679\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGdyZWVubWlsZXMtZXYvd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz81YmI5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDY3MDQ4Y2ZiNjc5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/signin/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signin/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\app\auth\signin\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_AuthErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthErrorBoundary */ \"(rsc)/./src/components/AuthErrorBoundary.tsx\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_ToastProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ToastProvider */ \"(rsc)/./src/components/ToastProvider.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"GreenMilesEV - Electric Vehicle Management\",\n    description: \"Comprehensive platform for electric vehicle management, charging, and trip planning\",\n    keywords: [\n        \"electric vehicle\",\n        \"EV\",\n        \"charging\",\n        \"green energy\",\n        \"sustainability\"\n    ],\n    authors: [\n        {\n            name: \"GreenMilesEV Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.AuthErrorBoundary, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-screen bg-background\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToastProvider__WEBPACK_IMPORTED_MODULE_5__.ToastProvider, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AuthErrorBoundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/AuthErrorBoundary.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthErrorBoundary: () => (/* binding */ e0),
/* harmony export */   useAuthErrorHandler: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx#AuthErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx#useAuthErrorHandler`);


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ThemeProvider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./src/components/ToastProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ToastProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ToastProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ToastProvider.tsx#ToastProvider`);


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#withAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/react-hot-toast","vendor-chunks/class-variance-authority","vendor-chunks/next-themes","vendor-chunks/webidl-conversions","vendor-chunks/goober","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fsignin%2Fpage&page=%2Fauth%2Fsignin%2Fpage&appPaths=%2Fauth%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();