/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!****************************************************************!*\
  !*** ../../node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \****************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?64b7\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CThemeProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CToastProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CThemeProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CToastProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AuthErrorBoundary.tsx */ \"(ssr)/./src/components/AuthErrorBoundary.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ToastProvider.tsx */ \"(ssr)/./src/components/ToastProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CThemeProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CToastProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/link.js */ \"(ssr)/../../node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q29iaV9jJTVDRGVza3RvcCU1Q2V2LWFwcCU1Q2FwcHMlNUN3ZWIlNUNzcmMlNUNjb21wb25lbnRzJTVDSGVhZGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q29iaV9jJTVDRGVza3RvcCU1Q2V2LWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDbGluay5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQThHO0FBQzlHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGdyZWVubWlsZXMtZXYvd2ViLz9kNjA0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcb2JpX2NcXFxcRGVza3RvcFxcXFxldi1hcHBcXFxcYXBwc1xcXFx3ZWJcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcSGVhZGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcb2JpX2NcXFxcRGVza3RvcFxcXFxldi1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q29iaV9jJTVDRGVza3RvcCU1Q2V2LWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q2FwcC1yb3V0ZXIuanMmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNvYmlfYyU1Q0Rlc2t0b3AlNUNldi1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q29iaV9jJTVDRGVza3RvcCU1Q2V2LWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q2xheW91dC1yb3V0ZXIuanMmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNvYmlfYyU1Q0Rlc2t0b3AlNUNldi1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNub3QtZm91bmQtYm91bmRhcnkuanMmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNvYmlfYyU1Q0Rlc2t0b3AlNUNldi1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDb2JpX2MlNUNEZXNrdG9wJTVDZXYtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDc3RhdGljLWdlbmVyYXRpb24tc2VhcmNocGFyYW1zLWJhaWxvdXQtcHJvdmlkZXIuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBPQUFtSTtBQUNuSSxrUEFBdUk7QUFDdkksZ1BBQXNJO0FBQ3RJLDBQQUEySTtBQUMzSSw4UUFBcUo7QUFDckoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZ3JlZW5taWxlcy1ldi93ZWIvPzgzZWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxvYmlfY1xcXFxEZXNrdG9wXFxcXGV2LWFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG9iaV9jXFxcXERlc2t0b3BcXFxcZXYtYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG9iaV9jXFxcXERlc2t0b3BcXFxcZXYtYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcb2JpX2NcXFxcRGVza3RvcFxcXFxldi1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG9iaV9jXFxcXERlc2t0b3BcXFxcZXYtYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcb2JpX2NcXFxcRGVza3RvcFxcXFxldi1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthErrorBoundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/AuthErrorBoundary.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthErrorBoundary: () => (/* binding */ AuthErrorBoundary),\n/* harmony export */   useAuthErrorHandler: () => (/* binding */ useAuthErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ AuthErrorBoundary,useAuthErrorHandler auto */ \n\n\n\n\nclass AuthErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.retry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"Authentication error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                error: this.state.error,\n                retry: this.retry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                lineNumber: 42,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, retry }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.AlertTitle, {\n                            children: \"Authentication Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.AlertDescription, {\n                            className: \"mt-2\",\n                            children: error.message || \"An unexpected error occurred during authentication.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: retry,\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>window.location.href = \"/\",\n                            className: \"flex-1\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n// Hook for handling auth errors in functional components\nfunction useAuthErrorHandler() {\n    const handleAuthError = (error)=>{\n        console.error(\"Auth error:\", error);\n        // Handle specific error types\n        if (error?.message?.includes(\"Invalid login credentials\")) {\n            return \"Invalid email or password. Please check your credentials and try again.\";\n        }\n        if (error?.message?.includes(\"Email not confirmed\")) {\n            return \"Please check your email and click the confirmation link before signing in.\";\n        }\n        if (error?.message?.includes(\"Too many requests\")) {\n            return \"Too many login attempts. Please wait a few minutes before trying again.\";\n        }\n        if (error?.message?.includes(\"Network\")) {\n            return \"Network error. Please check your internet connection and try again.\";\n        }\n        // Default error message\n        return error?.message || \"An unexpected error occurred. Please try again.\";\n    };\n    return {\n        handleAuthError\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\n\nfunction Header({ variant = \"default\" }) {\n    const { user, profile, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const handleSignOut = async ()=>{\n        await signOut();\n    };\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    // Navigation links for authenticated users\n    const authenticatedNavLinks = [\n        {\n            href: \"/dashboard\",\n            label: \"Dashboard\"\n        },\n        {\n            href: \"/ev-models\",\n            label: \"Browse EVs\"\n        },\n        {\n            href: \"/vehicles\",\n            label: \"My Vehicles\"\n        },\n        {\n            href: \"/charging\",\n            label: \"Charging\"\n        },\n        {\n            href: \"/analytics\",\n            label: \"Analytics\"\n        },\n        {\n            href: \"/profile\",\n            label: \"Profile\"\n        }\n    ];\n    // Navigation links for public pages\n    const publicNavLinks = [\n        {\n            href: \"/\",\n            label: \"Home\"\n        },\n        {\n            href: \"/ev-models\",\n            label: \"Browse EVs\"\n        },\n        {\n            href: \"/#features\",\n            label: \"Features\"\n        },\n        {\n            href: \"/#about\",\n            label: \"About\"\n        },\n        {\n            href: \"/#contact\",\n            label: \"Contact\"\n        }\n    ];\n    const navLinks = user ? authenticatedNavLinks : publicNavLinks;\n    if (variant === \"dashboard\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"border-b border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-16 items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: user ? \"/dashboard\" : \"/\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"mr-2 h-8 w-8 text-electric-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"GreenMilesEV\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden items-center space-x-6 md:flex\",\n                                children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: link.href,\n                                        className: `text-sm font-medium transition-colors ${isActive(link.href) ? \"text-electric-600\" : \"text-gray-600 hover:text-electric-600 dark:text-gray-300\"}`,\n                                        children: link.label\n                                    }, link.href, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden text-sm text-gray-600 dark:text-gray-300 sm:block\",\n                                                children: [\n                                                    \"Welcome, \",\n                                                    profile?.full_name || user?.email\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/settings\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: handleSignOut,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/auth/signin\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"sm\",\n                                                className: \"bg-electric-600 hover:bg-electric-700\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/auth/signup\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"md:hidden\",\n                                        onClick: toggleMobileMenu,\n                                        children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 37\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 65\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this),\n                    isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t bg-white dark:bg-gray-900 md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"space-y-2 px-4 py-4\",\n                            children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: link.href,\n                                    className: `block rounded-md px-3 py-2 text-sm font-medium transition-colors ${isActive(link.href) ? \"bg-electric-50 text-electric-600\" : \"text-gray-600 hover:bg-gray-50 hover:text-electric-600\"}`,\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: link.label\n                                }, link.href, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this);\n    }\n    // Default header for public pages\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"border-b border-gray-200 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 dark:border-gray-800 dark:bg-gray-900/95 dark:supports-[backdrop-filter]:bg-gray-900/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container flex h-16 items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-6 w-6 text-electric-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                children: \"GreenMilesEV\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden items-center space-x-6 md:flex\",\n                        children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: link.href,\n                                className: `text-sm font-medium transition-colors ${isActive(link.href) ? \"text-electric-600\" : \"text-gray-600 hover:text-electric-600 dark:text-gray-300\"}`,\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/dashboard\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleSignOut,\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/auth/signin\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        className: \"bg-electric-600 hover:bg-electric-700\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/auth/signup\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"md:hidden\",\n                                onClick: toggleMobileMenu,\n                                children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 33\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 61\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t bg-white dark:bg-gray-900 md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"container space-y-2 px-4 py-4\",\n                    children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: link.href,\n                            className: `block rounded-md px-3 py-2 text-sm font-medium transition-colors ${isActive(link.href) ? \"bg-electric-50 text-electric-600\" : \"text-gray-600 hover:bg-gray-50 hover:text-electric-600\"}`,\n                            onClick: ()=>setIsMobileMenuOpen(false),\n                            children: link.label\n                        }, link.href, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL0BncmVlbm1pbGVzLWV2L3dlYi8uL3NyYy9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4PzZmNDciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIgfSBmcm9tICduZXh0LXRoZW1lcydcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSAnbmV4dC10aGVtZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggle auto */ \n\n\n\n\nfunction ThemeToggle() {\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // useEffect only runs on the client, so now we can safely show the UI\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n            variant: \"ghost\",\n            size: \"sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n        variant: \"ghost\",\n        size: \"sm\",\n        onClick: ()=>setTheme(theme === \"light\" ? \"dark\" : \"light\"),\n        children: [\n            theme === \"light\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ToastProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ToastProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider auto */ \n\nfunction ToastProvider() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        position: \"top-right\",\n        toastOptions: {\n            // Default options\n            duration: 4000,\n            style: {\n                background: \"var(--background)\",\n                color: \"var(--foreground)\",\n                border: \"1px solid var(--border)\"\n            },\n            // Success toast styling\n            success: {\n                duration: 3000,\n                style: {\n                    background: \"rgb(34 197 94)\",\n                    color: \"white\",\n                    border: \"1px solid rgb(34 197 94)\"\n                },\n                iconTheme: {\n                    primary: \"white\",\n                    secondary: \"rgb(34 197 94)\"\n                }\n            },\n            // Error toast styling\n            error: {\n                duration: 5000,\n                style: {\n                    background: \"rgb(239 68 68)\",\n                    color: \"white\",\n                    border: \"1px solid rgb(239 68 68)\"\n                },\n                iconTheme: {\n                    primary: \"white\",\n                    secondary: \"rgb(239 68 68)\"\n                }\n            },\n            // Loading toast styling\n            loading: {\n                style: {\n                    background: \"rgb(59 130 246)\",\n                    color: \"white\",\n                    border: \"1px solid rgb(59 130 246)\"\n                },\n                iconTheme: {\n                    primary: \"white\",\n                    secondary: \"rgb(59 130 246)\"\n                }\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ToastProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ToastProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n            if (error) {\n                console.error(\"Error getting session:\", error);\n            }\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchProfile();\n            }\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state changed:\", event, session?.user?.email);\n            setSession(session);\n            setUser(session?.user ?? null);\n            try {\n                if (session?.user) {\n                    await fetchProfile();\n                } else {\n                    setProfile(null);\n                }\n            } catch (error) {\n                console.error(\"Error in auth state change handler:\", error);\n            } finally{\n                // Always set loading to false after handling auth state change\n                setLoading(false);\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const fetchProfile = async ()=>{\n        try {\n            console.log(\"Fetching user profile...\");\n            // Add timeout to prevent hanging\n            const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Profile fetch timeout\")), 10000));\n            const profilePromise = _lib_api__WEBPACK_IMPORTED_MODULE_3__.profileApi.getProfile();\n            const { data, error } = await Promise.race([\n                profilePromise,\n                timeoutPromise\n            ]);\n            if (error) {\n                console.error(\"Error fetching profile:\", error);\n            // Don't throw error - just log it and continue\n            } else {\n                console.log(\"Profile fetched successfully:\", data);\n                setProfile(data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n        // Don't throw error - just log it and continue\n        // This prevents the auth flow from hanging if profile fetch fails\n        }\n    };\n    const signUp = async (email, password, metadata)=>{\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: metadata\n                }\n            });\n            return {\n                data,\n                error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            console.log(\"Attempting sign in for:\", email);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                console.error(\"Sign in error:\", error);\n                setLoading(false) // Only set loading false on error\n                ;\n            } else {\n                console.log(\"Sign in successful, waiting for auth state change...\");\n            // Don't set loading false here - let the auth state change handler do it\n            }\n            return {\n                data,\n                error\n            };\n        } catch (err) {\n            console.error(\"Sign in exception:\", err);\n            setLoading(false);\n            throw err;\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            return {\n                error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetPassword = async (email)=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/reset-password`\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const signInWithGoogle = async ()=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n            provider: \"google\",\n            options: {\n                redirectTo: `${window.location.origin}/dashboard`\n            }\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const signInWithGitHub = async ()=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n            provider: \"github\",\n            options: {\n                redirectTo: `${window.location.origin}/dashboard`\n            }\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const updateProfile = async (updates)=>{\n        try {\n            const { data, error } = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.profileApi.updateProfile(updates);\n            if (data && !error) {\n                setProfile(data);\n            }\n            return {\n                data,\n                error\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    };\n    const refreshProfile = async ()=>{\n        await fetchProfile();\n    };\n    const value = {\n        user,\n        profile,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        signInWithGoogle,\n        signInWithGitHub,\n        updateProfile,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 224,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Higher-order component for protected routes\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { user, loading } = useAuth();\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex min-h-screen items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 w-32 animate-spin rounded-full border-b-2 border-electric-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            // Redirect to login page\n            window.location.href = \"/auth/signin\";\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 254,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsApi: () => (/* binding */ analyticsApi),\n/* harmony export */   chargingSessionApi: () => (/* binding */ chargingSessionApi),\n/* harmony export */   chargingStationApi: () => (/* binding */ chargingStationApi),\n/* harmony export */   maintenanceApi: () => (/* binding */ maintenanceApi),\n/* harmony export */   profileApi: () => (/* binding */ profileApi),\n/* harmony export */   tripApi: () => (/* binding */ tripApi),\n/* harmony export */   vehicleApi: () => (/* binding */ vehicleApi)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\n// Profile API\nconst profileApi = {\n    async getProfile () {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            return {\n                data: null,\n                error: {\n                    message: \"No authenticated user\"\n                }\n            };\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateProfile (updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", (await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser()).data.user?.id).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async uploadAvatar (file) {\n        try {\n            const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (!user) throw new Error(\"No user found\");\n            const fileExt = file.name.split(\".\").pop();\n            const fileName = `${user.id}-${Math.random()}.${fileExt}`;\n            const filePath = `avatars/${fileName}`;\n            // Upload file to storage\n            const { data: uploadData, error: uploadError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(\"avatars\").upload(filePath, file, {\n                cacheControl: \"3600\",\n                upsert: false\n            });\n            if (uploadError) throw uploadError;\n            // Get public URL\n            const { data: { publicUrl } } = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(\"avatars\").getPublicUrl(filePath);\n            // Update profile with new avatar URL\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").update({\n                avatar_url: publicUrl,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            return {\n                data,\n                error\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    },\n    async deleteAvatar () {\n        try {\n            const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (!user) throw new Error(\"No user found\");\n            // Get current profile to find avatar URL\n            const { data: profile } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").select(\"avatar_url\").eq(\"id\", user.id).single();\n            // Delete file from storage if it exists\n            if (profile?.avatar_url) {\n                const fileName = profile.avatar_url.split(\"/\").pop();\n                if (fileName) {\n                    await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(\"avatars\").remove([\n                        `avatars/${fileName}`\n                    ]);\n                }\n            }\n            // Update profile to remove avatar URL\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").update({\n                avatar_url: null,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            return {\n                data,\n                error\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    },\n    async getUserStats () {\n        try {\n            const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (!user) throw new Error(\"No user found\");\n            // Get vehicle count\n            const { count: vehicleCount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"user_id\", user.id);\n            // Get charging session count\n            const { count: chargingSessionCount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"user_id\", user.id);\n            // Get total miles from trips\n            const { data: tripsData } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(\"distance_miles\").eq(\"user_id\", user.id);\n            const totalMiles = tripsData?.reduce((sum, trip)=>sum + (trip.distance_miles || 0), 0) || 0;\n            return {\n                data: {\n                    vehicleCount: vehicleCount || 0,\n                    chargingSessionCount: chargingSessionCount || 0,\n                    totalMiles: Math.round(totalMiles)\n                },\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: {\n                    vehicleCount: 0,\n                    chargingSessionCount: 0,\n                    totalMiles: 0\n                },\n                error\n            };\n        }\n    }\n};\n// Vehicle API\nconst vehicleApi = {\n    async getVehicles () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        return {\n            data,\n            error\n        };\n    },\n    async getVehicle (id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\").eq(\"id\", id).single();\n        return {\n            data,\n            error\n        };\n    },\n    async createVehicle (vehicle) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").insert([\n            vehicle\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateVehicle (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async deleteVehicle (id) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").delete().eq(\"id\", id);\n        return {\n            error\n        };\n    }\n};\n// Charging Station API\nconst chargingStationApi = {\n    async getChargingStations (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_stations\").select(\"*\").eq(\"status\", \"operational\").limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async getNearbyStations (latitude, longitude, radiusMiles = 25) {\n        // Using PostGIS extension for geographic queries (if available)\n        // For now, we'll fetch all stations and filter client-side\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_stations\").select(\"*\").eq(\"status\", \"operational\");\n        if (data && !error) {\n            // Simple distance calculation (for production, use PostGIS)\n            const stationsWithDistance = data.map((station)=>({\n                    ...station,\n                    distance: calculateDistance(latitude, longitude, station.latitude, station.longitude)\n                })).filter((station)=>station.distance <= radiusMiles).sort((a, b)=>a.distance - b.distance);\n            return {\n                data: stationsWithDistance,\n                error\n            };\n        }\n        return {\n            data,\n            error\n        };\n    }\n};\n// Charging Session API\nconst chargingSessionApi = {\n    async getChargingSessions (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(`\n        *,\n        vehicle:vehicles(make, model, year),\n        charging_station:charging_stations(name, network, address)\n      `).order(\"start_time\", {\n            ascending: false\n        }).limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async createChargingSession (session) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").insert([\n            session\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateChargingSession (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Trip API\nconst tripApi = {\n    async getTrips (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(`\n        *,\n        vehicle:vehicles(make, model, year)\n      `).order(\"start_time\", {\n            ascending: false\n        }).limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async createTrip (trip) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").insert([\n            trip\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateTrip (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Maintenance API\nconst maintenanceApi = {\n    async getMaintenanceRecords (vehicleId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").select(`\n        *,\n        vehicle:vehicles(make, model, year)\n      `).order(\"service_date\", {\n            ascending: false\n        });\n        if (vehicleId) {\n            query = query.eq(\"vehicle_id\", vehicleId);\n        }\n        const { data, error } = await query;\n        return {\n            data,\n            error\n        };\n    },\n    async createMaintenanceRecord (record) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").insert([\n            record\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateMaintenanceRecord (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Analytics API\nconst analyticsApi = {\n    async getVehicleAnalytics (vehicleId, startDate, endDate) {\n        // Get trips for the vehicle\n        let tripsQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(\"*\").eq(\"vehicle_id\", vehicleId).eq(\"status\", \"completed\");\n        if (startDate) tripsQuery = tripsQuery.gte(\"start_time\", startDate);\n        if (endDate) tripsQuery = tripsQuery.lte(\"start_time\", endDate);\n        const { data: trips, error: tripsError } = await tripsQuery;\n        // Get charging sessions for the vehicle\n        let sessionsQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(\"*\").eq(\"vehicle_id\", vehicleId).eq(\"status\", \"completed\");\n        if (startDate) sessionsQuery = sessionsQuery.gte(\"start_time\", startDate);\n        if (endDate) sessionsQuery = sessionsQuery.lte(\"start_time\", endDate);\n        const { data: sessions, error: sessionsError } = await sessionsQuery;\n        if (tripsError || sessionsError) {\n            return {\n                data: null,\n                error: tripsError || sessionsError\n            };\n        }\n        // Calculate analytics\n        const totalMiles = trips?.reduce((sum, trip)=>sum + (trip.distance_miles || 0), 0) || 0;\n        const totalTrips = trips?.length || 0;\n        const totalEnergyUsed = trips?.reduce((sum, trip)=>sum + (trip.energy_used_kwh || 0), 0) || 0;\n        const totalChargingCost = sessions?.reduce((sum, session)=>sum + (session.cost_total || 0), 0) || 0;\n        const averageEfficiency = totalEnergyUsed > 0 ? totalMiles / totalEnergyUsed : 0;\n        // Estimate carbon and money savings (compared to average gas car)\n        const carbonSavedLbs = totalMiles * 0.89 // Rough estimate: 0.89 lbs CO2 per mile for gas car\n        ;\n        const moneySaved = totalMiles / 25 * 3.5 - totalChargingCost // Assume 25 MPG, $3.50/gallon\n        ;\n        const analytics = {\n            total_miles: Math.round(totalMiles * 10) / 10,\n            total_trips: totalTrips,\n            average_efficiency: Math.round(averageEfficiency * 100) / 100,\n            total_energy_used: Math.round(totalEnergyUsed * 10) / 10,\n            total_charging_cost: Math.round(totalChargingCost * 100) / 100,\n            carbon_saved_lbs: Math.round(carbonSavedLbs * 10) / 10,\n            money_saved: Math.round(moneySaved * 100) / 100\n        };\n        return {\n            data: analytics,\n            error: null\n        };\n    }\n};\n// Helper function for distance calculation\nfunction calculateDistance(lat1, lon1, lat2, lon2) {\n    const R = 3959 // Earth's radius in miles\n    ;\n    const dLat = toRadians(lat2 - lat1);\n    const dLon = toRadians(lon2 - lon1);\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n}\nfunction toRadians(degrees) {\n    return degrees * (Math.PI / 180);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://pbevpexclffmhqstwlha.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZXZwZXhjbGZmbWhxc3R3bGhhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjA1MzksImV4cCI6MjA2NTQ5NjUzOX0.F6bMKoeKV5QTyt0oEfNDn6-s780wkvvddplEBUuRpsI\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBR3BELE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUVqRSxJQUFJLENBQUNELGVBQWUsQ0FBQ0ksaUJBQWlCO0lBQ3BDLE1BQU0sSUFBSUUsTUFBTTtBQUNsQjtBQUVPLE1BQU1DLFdBQVdSLG1FQUFZQSxDQUFXQyxhQUFhSSxpQkFBaUI7SUFDM0VJLE1BQU07UUFDSkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLG9CQUFvQjtJQUN0QjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZ3JlZW5taWxlcy1ldi93ZWIvLi9zcmMvbGliL3N1cGFiYXNlLnRzPzA2ZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuaW1wb3J0IHR5cGUgeyBEYXRhYmFzZSB9IGZyb20gJ0Avc2hhcmVkL3R5cGVzJ1xuXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCFcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuXG5pZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUFub25LZXkpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIFN1cGFiYXNlIGVudmlyb25tZW50IHZhcmlhYmxlcycpXG59XG5cbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudDxEYXRhYmFzZT4oc3VwYWJhc2VVcmwsIHN1cGFiYXNlQW5vbktleSwge1xuICBhdXRoOiB7XG4gICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSxcbiAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcbiAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUsXG4gIH0sXG59KVxuXG4vLyBFeHBvcnQgdHlwZXMgZm9yIGNvbnZlbmllbmNlXG5leHBvcnQgdHlwZSB7IERhdGFiYXNlIH0gZnJvbSAnQC9zaGFyZWQvdHlwZXMnXG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJFcnJvciIsInN1cGFiYXNlIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiIsImRldGVjdFNlc3Npb25JblVybCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BncmVlbm1pbGVzLWV2L3dlYi8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"067048cfb679\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGdyZWVubWlsZXMtZXYvd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz81YmI5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDY3MDQ4Y2ZiNjc5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_AuthErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthErrorBoundary */ \"(rsc)/./src/components/AuthErrorBoundary.tsx\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_ToastProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ToastProvider */ \"(rsc)/./src/components/ToastProvider.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"GreenMilesEV - Electric Vehicle Management\",\n    description: \"Comprehensive platform for electric vehicle management, charging, and trip planning\",\n    keywords: [\n        \"electric vehicle\",\n        \"EV\",\n        \"charging\",\n        \"green energy\",\n        \"sustainability\"\n    ],\n    authors: [\n        {\n            name: \"GreenMilesEV Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.AuthErrorBoundary, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-screen bg-background\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToastProvider__WEBPACK_IMPORTED_MODULE_5__.ToastProvider, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(rsc)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,Leaf,MapPin,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/lucide-react/dist/esm/icons/leaf.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,Leaf,MapPin,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,Leaf,MapPin,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,Leaf,MapPin,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,Leaf,MapPin,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,Leaf,MapPin,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/lucide-react/dist/esm/icons/users.js\");\n\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"flex flex-1 items-center justify-center bg-gradient-to-br from-electric-50 to-green-50 py-20 dark:from-gray-900 dark:to-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container space-y-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                            variant: \"secondary\",\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"mr-1 h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                \"Sustainable Transportation\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold tracking-tight text-gray-900 dark:text-white md:text-6xl\",\n                            children: [\n                                \"Drive the Future with \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-electric-600\",\n                                    children: \"Electric Vehicles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 35\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mx-auto max-w-2xl text-xl text-gray-600 dark:text-gray-300\",\n                            children: \"Comprehensive platform for electric vehicle management, charging station discovery, and sustainable transportation planning.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col justify-center gap-4 sm:flex-row\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    size: \"lg\",\n                                    className: \"bg-electric-600 hover:bg-electric-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Start Your Journey\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    size: \"lg\",\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Find Charging Stations\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"bg-white py-20 dark:bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-16 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"mb-4 text-3xl font-bold text-gray-900 dark:text-white md:text-4xl\",\n                                    children: \"Everything You Need for EV Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300\",\n                                    children: \"From vehicle tracking to charging optimization, we provide all the tools for a seamless electric vehicle experience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-8 md:grid-cols-2 lg:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-0 shadow-lg transition-shadow hover:shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"mb-2 h-10 w-10 text-electric-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Vehicle Management\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Track your EV's performance, maintenance, and efficiency metrics in real-time.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-0 shadow-lg transition-shadow hover:shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"mb-2 h-10 w-10 text-electric-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Charging Network\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Find nearby charging stations with real-time availability and pricing information.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-0 shadow-lg transition-shadow hover:shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"mb-2 h-10 w-10 text-electric-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Analytics Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Monitor energy consumption, cost savings, and environmental impact.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-0 shadow-lg transition-shadow hover:shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mb-2 h-10 w-10 text-electric-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Smart Charging\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Optimize charging schedules based on energy prices and grid demand.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-0 shadow-lg transition-shadow hover:shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"mb-2 h-10 w-10 text-electric-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Carbon Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Track your carbon footprint reduction and environmental contributions.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-0 shadow-lg transition-shadow hover:shadow-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_Leaf_MapPin_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mb-2 h-10 w-10 text-electric-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Community\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Connect with other EV owners and share experiences and tips.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_5__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AuthErrorBoundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/AuthErrorBoundary.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthErrorBoundary: () => (/* binding */ e0),
/* harmony export */   useAuthErrorHandler: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx#AuthErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx#useAuthErrorHandler`);


/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Zap!=!lucide-react */ \"(rsc)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t border-gray-200 bg-gray-50 py-12 dark:border-gray-800 dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-between md:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"mb-4 flex items-center space-x-2 md:mb-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-5 w-5 text-electric-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                children: \"GreenMilesEV\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-300\",\n                        children: \"\\xa9 2024 GreenMilesEV. All rights reserved. Building a sustainable future.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNEI7QUFDTTtBQUUzQixTQUFTRTtJQUNkLHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDSixrREFBSUE7d0JBQUNNLE1BQUs7d0JBQUlGLFdBQVU7OzBDQUN2Qiw4REFBQ0gsK0VBQUdBO2dDQUFDRyxXQUFVOzs7Ozs7MENBQ2YsOERBQUNHO2dDQUFLSCxXQUFVOzBDQUE4Qzs7Ozs7Ozs7Ozs7O2tDQUVoRSw4REFBQ0k7d0JBQUVKLFdBQVU7a0NBQTJDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2xFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGdyZWVubWlsZXMtZXYvd2ViLy4vc3JjL2NvbXBvbmVudHMvRm9vdGVyLnRzeD8zNTFmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IFphcCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuZXhwb3J0IGZ1bmN0aW9uIEZvb3RlcigpIHtcbiAgcmV0dXJuIChcbiAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBiZy1ncmF5LTUwIHB5LTEyIGRhcms6Ym9yZGVyLWdyYXktODAwIGRhcms6YmctZ3JheS05MDBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1kOmZsZXgtcm93XCI+XG4gICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJtYi00IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtZDptYi0wXCI+XG4gICAgICAgICAgICA8WmFwIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1lbGVjdHJpYy02MDBcIiAvPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPkdyZWVuTWlsZXNFVjwvc3Bhbj5cbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgwqkgMjAyNCBHcmVlbk1pbGVzRVYuIEFsbCByaWdodHMgcmVzZXJ2ZWQuIEJ1aWxkaW5nIGEgc3VzdGFpbmFibGUgZnV0dXJlLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvb3Rlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkxpbmsiLCJaYXAiLCJGb290ZXIiLCJmb290ZXIiLCJjbGFzc05hbWUiLCJkaXYiLCJocmVmIiwic3BhbiIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\Header.tsx#Header`);


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ThemeProvider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./src/components/ToastProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ToastProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ToastProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ToastProvider.tsx#ToastProvider`);


/***/ }),

/***/ "(rsc)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#withAuth`);


/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/../../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/../../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BncmVlbm1pbGVzLWV2L3dlYi8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/react-hot-toast","vendor-chunks/class-variance-authority","vendor-chunks/next-themes","vendor-chunks/webidl-conversions","vendor-chunks/goober","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();