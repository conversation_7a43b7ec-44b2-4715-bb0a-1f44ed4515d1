'use client'

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import type { EVModelWithDetails } from '@/shared/types'

interface EVModelCostAnalysisProps {
  evModel: EVModelWithDetails
}

export function EVModelCostAnalysis({ evModel }: EVModelCostAnalysisProps) {
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Cost Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 dark:text-gray-400">
            Total cost of ownership calculator and analysis coming soon...
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
