/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_comparison_ComparisonToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/comparison/ComparisonToggle */ \"(app-pages-browser)/./src/components/comparison/ComparisonToggle.tsx\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Header(param) {\n    let { variant = \"default\" } = param;\n    _s();\n    const { user, profile, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const handleSignOut = async ()=>{\n        await signOut();\n    };\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    // Navigation links for authenticated users\n    const authenticatedNavLinks = [\n        {\n            href: \"/dashboard\",\n            label: \"Dashboard\"\n        },\n        {\n            href: \"/ev-models\",\n            label: \"Browse EVs\"\n        },\n        {\n            href: \"/vehicles\",\n            label: \"My Vehicles\"\n        },\n        {\n            href: \"/charging\",\n            label: \"Charging\"\n        },\n        {\n            href: \"/analytics\",\n            label: \"Analytics\"\n        },\n        {\n            href: \"/profile\",\n            label: \"Profile\"\n        }\n    ];\n    // Navigation links for public pages\n    const publicNavLinks = [\n        {\n            href: \"/\",\n            label: \"Home\"\n        },\n        {\n            href: \"/ev-models\",\n            label: \"Browse EVs\"\n        },\n        {\n            href: \"/#features\",\n            label: \"Features\"\n        },\n        {\n            href: \"/#about\",\n            label: \"About\"\n        },\n        {\n            href: \"/#contact\",\n            label: \"Contact\"\n        }\n    ];\n    const navLinks = user ? authenticatedNavLinks : publicNavLinks;\n    if (variant === \"dashboard\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"border-b border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-16 items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: user ? \"/dashboard\" : \"/\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mr-2 h-8 w-8 text-electric-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"GreenMilesEV\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden items-center space-x-6 md:flex\",\n                                children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: link.href,\n                                        className: \"text-sm font-medium transition-colors \".concat(isActive(link.href) ? \"text-electric-600\" : \"text-gray-600 hover:text-electric-600 dark:text-gray-300\"),\n                                        children: link.label\n                                    }, link.href, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden text-sm text-gray-600 dark:text-gray-300 sm:block\",\n                                                children: [\n                                                    \"Welcome, \",\n                                                    (profile === null || profile === void 0 ? void 0 : profile.full_name) || (user === null || user === void 0 ? void 0 : user.email)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comparison_ComparisonToggle__WEBPACK_IMPORTED_MODULE_5__.ComparisonToggle, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/settings\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: handleSignOut,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/auth/signin\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"sm\",\n                                                className: \"bg-electric-600 hover:bg-electric-700\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/auth/signup\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"md:hidden\",\n                                        onClick: toggleMobileMenu,\n                                        children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 37\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 65\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this),\n                    isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t bg-white dark:bg-gray-900 md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"space-y-2 px-4 py-4\",\n                            children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: link.href,\n                                    className: \"block rounded-md px-3 py-2 text-sm font-medium transition-colors \".concat(isActive(link.href) ? \"bg-electric-50 text-electric-600\" : \"text-gray-600 hover:bg-gray-50 hover:text-electric-600\"),\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: link.label\n                                }, link.href, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this);\n    }\n    // Default header for public pages\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"border-b border-gray-200 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 dark:border-gray-800 dark:bg-gray-900/95 dark:supports-[backdrop-filter]:bg-gray-900/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container flex h-16 items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-6 w-6 text-electric-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                children: \"GreenMilesEV\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden items-center space-x-6 md:flex\",\n                        children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: link.href,\n                                className: \"text-sm font-medium transition-colors \".concat(isActive(link.href) ? \"text-electric-600\" : \"text-gray-600 hover:text-electric-600 dark:text-gray-300\"),\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comparison_ComparisonToggle__WEBPACK_IMPORTED_MODULE_5__.ComparisonToggle, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/dashboard\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleSignOut,\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/auth/signin\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        className: \"bg-electric-600 hover:bg-electric-700\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/auth/signup\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"md:hidden\",\n                                onClick: toggleMobileMenu,\n                                children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 33\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 61\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t bg-white dark:bg-gray-900 md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"container space-y-2 px-4 py-4\",\n                    children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: link.href,\n                            className: \"block rounded-md px-3 py-2 text-sm font-medium transition-colors \".concat(isActive(link.href) ? \"bg-electric-50 text-electric-600\" : \"text-gray-600 hover:bg-gray-50 hover:text-electric-600\"),\n                            onClick: ()=>setIsMobileMenuOpen(false),\n                            children: link.label\n                        }, link.href, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"NPYR+wIRNrBlCl4bA5370476eLI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/comparison/ComparisonToggle.tsx":
/*!********************************************************!*\
  !*** ./src/components/comparison/ComparisonToggle.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComparisonToggle: function() { return /* binding */ ComparisonToggle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Compare_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Compare!=!lucide-react */ \"(app-pages-browser)/__barrel_optimize__?names=Compare!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Compare_lucide_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_barrel_optimize_names_Compare_lucide_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ComparisonContext */ \"(app-pages-browser)/./src/contexts/ComparisonContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ ComparisonToggle auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ComparisonToggle(param) {\n    let { className } = param;\n    _s();\n    const { state, togglePanel } = (0,_contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_4__.useComparison)();\n    if (state.models.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n        variant: state.isOpen ? \"default\" : \"outline\",\n        onClick: togglePanel,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative transition-colors\", state.isOpen && \"bg-electric-600 text-white hover:bg-electric-700\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Compare_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Compare, {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonToggle.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            \"Compare\",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                variant: state.isOpen ? \"secondary\" : \"default\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"ml-2 h-5 w-5 rounded-full p-0 text-xs\", state.isOpen && \"bg-white text-electric-600\"),\n                children: state.models.length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonToggle.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonToggle.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(ComparisonToggle, \"ms7d95nhrTquxY89QuzAxp1j4oo=\", false, function() {\n    return [\n        _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_4__.useComparison\n    ];\n});\n_c = ComparisonToggle;\nvar _c;\n$RefreshReg$(_c, \"ComparisonToggle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/comparison/ComparisonToggle.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ComparisonContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/ComparisonContext.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComparisonProvider: function() { return /* binding */ ComparisonProvider; },\n/* harmony export */   MAX_COMPARISON_MODELS: function() { return /* binding */ MAX_COMPARISON_MODELS; },\n/* harmony export */   useComparison: function() { return /* binding */ useComparison; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ComparisonProvider,useComparison,MAX_COMPARISON_MODELS auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Maximum number of models that can be compared at once\nconst MAX_COMPARISON_MODELS = 4;\n// Initial state\nconst initialState = {\n    models: [],\n    isOpen: false,\n    maxReached: false\n};\n// Comparison reducer\nfunction comparisonReducer(state, action) {\n    switch(action.type){\n        case \"ADD_MODEL\":\n            {\n                // Don't add if already exists or max reached\n                if (state.models.some((model)=>model.id === action.payload.id)) {\n                    return state;\n                }\n                if (state.models.length >= MAX_COMPARISON_MODELS) {\n                    return {\n                        ...state,\n                        maxReached: true\n                    };\n                }\n                const newModels = [\n                    ...state.models,\n                    action.payload\n                ];\n                return {\n                    ...state,\n                    models: newModels,\n                    maxReached: newModels.length >= MAX_COMPARISON_MODELS\n                };\n            }\n        case \"REMOVE_MODEL\":\n            {\n                const newModels = state.models.filter((model)=>model.id !== action.payload);\n                return {\n                    ...state,\n                    models: newModels,\n                    maxReached: false,\n                    isOpen: newModels.length > 0 ? state.isOpen : false\n                };\n            }\n        case \"CLEAR_ALL\":\n            return {\n                ...state,\n                models: [],\n                maxReached: false,\n                isOpen: false\n            };\n        case \"TOGGLE_PANEL\":\n            return {\n                ...state,\n                isOpen: state.models.length > 0 ? !state.isOpen : false\n            };\n        case \"OPEN_PANEL\":\n            return {\n                ...state,\n                isOpen: state.models.length > 0\n            };\n        case \"CLOSE_PANEL\":\n            return {\n                ...state,\n                isOpen: false\n            };\n        case \"LOAD_FROM_STORAGE\":\n            return {\n                ...state,\n                models: action.payload.slice(0, MAX_COMPARISON_MODELS),\n                maxReached: action.payload.length >= MAX_COMPARISON_MODELS\n            };\n        default:\n            return state;\n    }\n}\n// Create context\nconst ComparisonContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Local storage key\nconst STORAGE_KEY = \"ev-comparison-models\";\nfunction ComparisonProvider(param) {\n    let { children } = param;\n    _s();\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(comparisonReducer, initialState);\n    // Load from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const stored = localStorage.getItem(STORAGE_KEY);\n            if (stored) {\n                const models = JSON.parse(stored);\n                dispatch({\n                    type: \"LOAD_FROM_STORAGE\",\n                    payload: models\n                });\n            }\n        } catch (error) {\n            console.error(\"Failed to load comparison models from storage:\", error);\n        }\n    }, []);\n    // Save to localStorage whenever models change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(state.models));\n        } catch (error) {\n            console.error(\"Failed to save comparison models to storage:\", error);\n        }\n    }, [\n        state.models\n    ]);\n    // Action creators\n    const addModel = (model)=>{\n        dispatch({\n            type: \"ADD_MODEL\",\n            payload: model\n        });\n    };\n    const removeModel = (modelId)=>{\n        dispatch({\n            type: \"REMOVE_MODEL\",\n            payload: modelId\n        });\n    };\n    const clearAll = ()=>{\n        dispatch({\n            type: \"CLEAR_ALL\"\n        });\n    };\n    const togglePanel = ()=>{\n        dispatch({\n            type: \"TOGGLE_PANEL\"\n        });\n    };\n    const openPanel = ()=>{\n        dispatch({\n            type: \"OPEN_PANEL\"\n        });\n    };\n    const closePanel = ()=>{\n        dispatch({\n            type: \"CLOSE_PANEL\"\n        });\n    };\n    const isModelInComparison = (modelId)=>{\n        return state.models.some((model)=>model.id === modelId);\n    };\n    const canAddMore = state.models.length < MAX_COMPARISON_MODELS;\n    const comparisonCount = state.models.length;\n    const contextValue = {\n        state,\n        addModel,\n        removeModel,\n        clearAll,\n        togglePanel,\n        openPanel,\n        closePanel,\n        isModelInComparison,\n        canAddMore,\n        comparisonCount\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComparisonContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\ComparisonContext.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n_s(ComparisonProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = ComparisonProvider;\n// Custom hook to use comparison context\nfunction useComparison() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ComparisonContext);\n    if (context === undefined) {\n        throw new Error(\"useComparison must be used within a ComparisonProvider\");\n    }\n    return context;\n}\n_s1(useComparison, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Export constants for use in other components\n\nvar _c;\n$RefreshReg$(_c, \"ComparisonProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ComparisonContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=Compare!=!../../node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************!*\
  !*** __barrel_optimize__?names=Compare!=!../../node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {



;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});