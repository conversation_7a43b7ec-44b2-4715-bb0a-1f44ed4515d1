"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"58ecd21c0e62\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NjM4MCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjU4ZWNkMjFjMGU2MlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/comparison/ComparisonPanel.tsx":
/*!*******************************************************!*\
  !*** ./src/components/comparison/ComparisonPanel.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComparisonPanel: function() { return /* binding */ ComparisonPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,GitCompare,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/git-compare.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,GitCompare,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,GitCompare,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,GitCompare,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,GitCompare,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,GitCompare,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/ComparisonContext */ \"(app-pages-browser)/./src/contexts/ComparisonContext.tsx\");\n/* harmony import */ var _hooks_useComparisonActions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useComparisonActions */ \"(app-pages-browser)/./src/hooks/useComparisonActions.ts\");\n/* harmony import */ var _shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/shared/utils/ev-buyer-guide */ \"(app-pages-browser)/../../packages/shared/src/utils/ev-buyer-guide.ts\");\n/* harmony import */ var _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/shared/constants/ev-buyer-guide */ \"(app-pages-browser)/../../packages/shared/src/constants/ev-buyer-guide.ts\");\n/* __next_internal_client_entry_do_not_use__ ComparisonPanel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ComparisonPanel() {\n    _s();\n    const { state, togglePanel, closePanel } = (0,_contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_8__.useComparison)();\n    const { removeFromComparison, clearComparison } = (0,_hooks_useComparisonActions__WEBPACK_IMPORTED_MODULE_9__.useComparisonActions)();\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (state.models.length === 0) {\n        return null;\n    }\n    const handleToggleMinimize = ()=>{\n        setIsMinimized(!isMinimized);\n    };\n    const handleCompareAll = ()=>{\n        // Navigate to comparison page with all models\n        const modelIds = state.models.map((m)=>m.id).join(\",\");\n        window.open(\"/compare?models=\".concat(modelIds), \"_blank\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)] transition-all duration-300\", state.isOpen ? \"translate-y-0\" : \"translate-y-full\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"border-electric-200 shadow-2xl dark:border-electric-800\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    className: \"pb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"flex items-center gap-2 text-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5 text-electric-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Compare EVs\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"ml-2\",\n                                        children: [\n                                            state.models.length,\n                                            \"/4\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleToggleMinimize,\n                                        className: \"h-8 w-8 p-0\",\n                                        children: isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: closePanel,\n                                        className: \"h-8 w-8 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-64 space-y-3 overflow-y-auto\",\n                            children: state.models.map((model)=>{\n                                var _model_images;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 rounded-lg border p-3 hover:bg-gray-50 dark:hover:bg-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-12 w-16 shrink-0 overflow-hidden rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: ((_model_images = model.images) === null || _model_images === void 0 ? void 0 : _model_images[0]) || _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_11__.PLACEHOLDER_IMAGES.ev_model,\n                                                alt: \"\".concat(model.make, \" \").concat(model.model),\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-0 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"truncate text-sm font-medium\",\n                                                    children: [\n                                                        model.make,\n                                                        \" \",\n                                                        model.model\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: [\n                                                        (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_10__.formatPrice)(model.price_msrp),\n                                                        \" • \",\n                                                        (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_10__.formatRange)(model.range_epa_miles)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    className: \"h-8 w-8 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/ev-models/\".concat(model.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>removeFromComparison(model.id),\n                                                    className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, model.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 border-t pt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleCompareAll,\n                                    className: \"flex-1\",\n                                    disabled: state.models.length < 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Compare All\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: clearComparison,\n                                    className: \"text-red-600 hover:text-red-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_GitCompare_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this),\n                        state.models.length < 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-xs text-gray-600 dark:text-gray-400\",\n                            children: \"Add at least 2 models to start comparing\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(ComparisonPanel, \"P4O5Fpyx5RmVhHbTYYyO2XYirf8=\", false, function() {\n    return [\n        _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_8__.useComparison,\n        _hooks_useComparisonActions__WEBPACK_IMPORTED_MODULE_9__.useComparisonActions\n    ];\n});\n_c = ComparisonPanel;\nvar _c;\n$RefreshReg$(_c, \"ComparisonPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/comparison/ComparisonPanel.tsx\n"));

/***/ })

});