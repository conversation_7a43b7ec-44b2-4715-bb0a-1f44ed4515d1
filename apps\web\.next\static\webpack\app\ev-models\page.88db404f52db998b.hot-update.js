"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ev-models/page",{

/***/ "(app-pages-browser)/./src/components/ev-models/EVModelCard.tsx":
/*!**************************************************!*\
  !*** ./src/components/ev-models/EVModelCard.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EVModelCard: function() { return /* binding */ EVModelCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/battery.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/shared/utils/ev-buyer-guide */ \"(app-pages-browser)/../../packages/shared/src/utils/ev-buyer-guide.ts\");\n/* harmony import */ var _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/shared/constants/ev-buyer-guide */ \"(app-pages-browser)/../../packages/shared/src/constants/ev-buyer-guide.ts\");\n/* __next_internal_client_entry_do_not_use__ EVModelCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EVModelCard(param) {\n    let { model, viewMode, onAddToFavorites, isFavorite = false } = param;\n    var _model_images, _model_ev_manufacturers;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const primaryImage = ((_model_images = model.images) === null || _model_images === void 0 ? void 0 : _model_images[0]) || _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.ev_model;\n    const manufacturerLogo = ((_model_ev_manufacturers = model.ev_manufacturers) === null || _model_ev_manufacturers === void 0 ? void 0 : _model_ev_manufacturers.logo_url) || _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.manufacturer_logo;\n    const handleAddToFavorites = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        onAddToFavorites === null || onAddToFavorites === void 0 ? void 0 : onAddToFavorites(model.id);\n    };\n    const modelUrl = \"/ev-models/\".concat(model.id);\n    if (viewMode === \"list\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"overflow-hidden transition-shadow hover:shadow-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                href: modelUrl,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative h-32 w-48 shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: imageError ? _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.ev_model : primaryImage,\n                                    alt: \"\".concat(model.make, \" \").concat(model.model),\n                                    fill: true,\n                                    className: \"object-cover\",\n                                    onError: ()=>setImageError(true),\n                                    onLoad: ()=>setImageLoading(false)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 animate-pulse bg-gray-200 dark:bg-gray-700\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-2 top-2 flex flex-col gap-1\",\n                                    children: [\n                                        model.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: \"bg-electric-600 text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Featured\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 19\n                                        }, this),\n                                        model.best_value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            variant: \"secondary\",\n                                            children: \"Best Value\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 38\n                                        }, this),\n                                        model.editor_choice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: \"bg-amber-600 text-white\",\n                                            children: \"Editor's Choice\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 flex-col justify-between p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                manufacturerLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    src: manufacturerLogo,\n                                                                    alt: model.make,\n                                                                    width: 24,\n                                                                    height: 24,\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 90,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: model.make\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold\",\n                                                            children: [\n                                                                model.model,\n                                                                \" \",\n                                                                model.trim && \"(\".concat(model.trim, \")\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: [\n                                                                model.year,\n                                                                \" • \",\n                                                                model.body_type\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-electric-600\",\n                                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatPrice)(model.price_msrp)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        model.price_base && model.price_base !== model.price_msrp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: [\n                                                                \"From \",\n                                                                (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatPrice)(model.price_base)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 md:grid-cols-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-electric-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatRange)(model.range_epa_miles)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"EPA Range\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 text-electric-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: model.charging_speed_dc_kw ? \"\".concat(model.charging_speed_dc_kw, \"kW\") : \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"DC Charging\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-electric-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatChargingTime)(model.charging_time_10_80_minutes)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"10-80%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-4 text-electric-600\",\n                                                            children: \"⚡\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatAcceleration)(model.acceleration_0_60_mph)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"0-60 mph\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: handleAddToFavorites,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"transition-colors\", isFavorite && \"bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-950 dark:text-red-400\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-4 w-4\", isFavorite && \"fill-current\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: handleAddToComparison,\n                                                    disabled: isInComparison,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"transition-colors\", isInComparison && \"dark:bg-electric-950 bg-electric-50 text-electric-600\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        isInComparison ? \"Added\" : \"Compare\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: modelUrl,\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this);\n    }\n    // Grid view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"group overflow-hidden transition-all hover:shadow-lg hover:shadow-electric-600/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n            href: modelUrl,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative aspect-video overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            src: imageError ? _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.ev_model : primaryImage,\n                            alt: \"\".concat(model.make, \" \").concat(model.model),\n                            fill: true,\n                            className: \"object-cover transition-transform group-hover:scale-105\",\n                            onError: ()=>setImageError(true),\n                            onLoad: ()=>setImageLoading(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 animate-pulse bg-gray-200 dark:bg-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-3 top-3 flex flex-col gap-1\",\n                            children: [\n                                model.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    className: \"bg-electric-600 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"mr-1 h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Featured\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this),\n                                model.best_value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    variant: \"secondary\",\n                                    children: \"Best Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 34\n                                }, this),\n                                model.editor_choice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    className: \"bg-amber-600 text-white\",\n                                    children: \"Editor's Choice\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-3 top-3 flex flex-col gap-2 opacity-0 transition-opacity group-hover:opacity-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAddToFavorites,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-8 w-8 bg-white/90 backdrop-blur-sm hover:bg-white\", isFavorite && \"bg-red-50 text-red-600 hover:bg-red-100\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-4 w-4\", isFavorite && \"fill-current\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAddToComparison,\n                                    disabled: isInComparison,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-8 w-8 bg-white/90 backdrop-blur-sm hover:bg-white\", isInComparison && \"bg-electric-50 text-electric-600\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    className: \"pb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            manufacturerLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: manufacturerLogo,\n                                                alt: model.make,\n                                                width: 20,\n                                                height: 20,\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: model.make\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold leading-tight\",\n                                        children: model.model\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    model.trim && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: model.trim\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-electric-600\",\n                                        children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatPrice)(model.price_msrp)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                        children: model.year\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"pt-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"Range\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatRange)(model.range_epa_miles)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"Charging\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatChargingTime)(model.charging_time_10_80_minutes)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                model.acceleration_0_60_mph && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"0-60 mph\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatAcceleration)(model.acceleration_0_60_mph)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            className: \"mt-4 w-full\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: modelUrl,\n                                children: \"View Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n            lineNumber: 209,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s(EVModelCard, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = EVModelCard;\nvar _c;\n$RefreshReg$(_c, \"EVModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ev-models/EVModelCard.tsx\n"));

/***/ })

});