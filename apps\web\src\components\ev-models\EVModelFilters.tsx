'use client'

import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { X, Filter, RotateCcw } from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  EV_BODY_TYPES,
  PRODUCTION_STATUS,
  PRICE_RANGES,
  RANGE_CATEGORIES,
  CHARGING_SPEED_CATEGORIES,
  FILTER_PRESETS,
  POPULAR_EV_MAKES,
  BODY_TYPE_LABELS,
  PRODUCTION_STATUS_LABELS
} from '@/shared/constants/ev-buyer-guide'
import { formatPrice } from '@/shared/utils/ev-buyer-guide'
import type { EVModelFilters as FilterType, EVBodyType, ProductionStatus } from '@/shared/types'

interface EVModelFiltersProps {
  filters: FilterType
  onChange: (filters: Partial<FilterType>) => void
  onClose?: () => void
  className?: string
}

export function EVModelFilters({
  filters,
  onChange,
  onClose,
  className
}: EVModelFiltersProps) {
  const [priceRange, setPriceRange] = useState([
    filters.priceMin ? parseInt(filters.priceMin) : 0,
    filters.priceMax ? parseInt(filters.priceMax) : 20000000 // $200k
  ])

  const [rangeMin, setRangeMin] = useState(
    filters.rangeMin ? parseInt(filters.rangeMin) : 0
  )

  const handleMakeChange = (make: string) => {
    onChange({ make: make === 'all' ? undefined : make })
  }

  const handleBodyTypeChange = (bodyType: string) => {
    onChange({ bodyType: bodyType === 'all' ? undefined : bodyType })
  }

  const handleProductionStatusChange = (status: string) => {
    onChange({ productionStatus: status === 'all' ? undefined : status })
  }

  const handlePriceRangeChange = (values: number[]) => {
    setPriceRange(values)
    onChange({
      priceMin: values[0] > 0 ? values[0].toString() : undefined,
      priceMax: values[1] < 20000000 ? values[1].toString() : undefined
    })
  }

  const handleRangeMinChange = (values: number[]) => {
    const value = values[0]
    setRangeMin(value)
    onChange({
      rangeMin: value > 0 ? value.toString() : undefined
    })
  }

  const handleFeaturedChange = (checked: boolean) => {
    onChange({ featured: checked ? 'true' : undefined })
  }

  const handleBestValueChange = (checked: boolean) => {
    onChange({ bestValue: checked ? 'true' : undefined })
  }

  const handlePresetApply = (preset: typeof FILTER_PRESETS[0]) => {
    const newFilters: Partial<FilterType> = {}
    
    if ('priceMax' in preset.filters) {
      newFilters.priceMax = preset.filters.priceMax?.toString()
      setPriceRange([0, preset.filters.priceMax || 20000000])
    }
    
    if ('rangeMin' in preset.filters) {
      newFilters.rangeMin = preset.filters.rangeMin?.toString()
      setRangeMin(preset.filters.rangeMin || 0)
    }
    
    if ('bodyTypes' in preset.filters && preset.filters.bodyTypes) {
      newFilters.bodyType = preset.filters.bodyTypes[0]
    }
    
    if ('bestValue' in preset.filters) {
      newFilters.bestValue = 'true'
    }
    
    if ('productionStatus' in preset.filters) {
      newFilters.productionStatus = preset.filters.productionStatus
    }

    onChange(newFilters)
  }

  const handleClearAll = () => {
    setPriceRange([0, 20000000])
    setRangeMin(0)
    onChange({
      make: undefined,
      bodyType: undefined,
      priceMin: undefined,
      priceMax: undefined,
      rangeMin: undefined,
      productionStatus: undefined,
      featured: undefined,
      bestValue: undefined
    })
  }

  const hasActiveFilters = Object.values(filters).some(value => value !== undefined)

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAll}
                className="text-red-600 hover:text-red-700"
              >
                <RotateCcw className="mr-1 h-4 w-4" />
                Clear
              </Button>
            )}
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Filter Presets */}
        <div>
          <Label className="text-sm font-medium">Quick Filters</Label>
          <div className="mt-2 flex flex-wrap gap-2">
            {FILTER_PRESETS.map((preset) => (
              <Button
                key={preset.name}
                variant="outline"
                size="sm"
                onClick={() => handlePresetApply(preset)}
                className="text-xs"
              >
                {preset.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Make */}
        <div>
          <Label className="text-sm font-medium">Make</Label>
          <Select value={filters.make || 'all'} onValueChange={handleMakeChange}>
            <SelectTrigger className="mt-2">
              <SelectValue placeholder="All makes" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All makes</SelectItem>
              {POPULAR_EV_MAKES.map((make) => (
                <SelectItem key={make} value={make}>
                  {make}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Body Type */}
        <div>
          <Label className="text-sm font-medium">Body Type</Label>
          <Select value={filters.bodyType || 'all'} onValueChange={handleBodyTypeChange}>
            <SelectTrigger className="mt-2">
              <SelectValue placeholder="All body types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All body types</SelectItem>
              {EV_BODY_TYPES.map((type) => (
                <SelectItem key={type} value={type}>
                  {BODY_TYPE_LABELS[type]}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Price Range */}
        <div>
          <Label className="text-sm font-medium">
            Price Range: {formatPrice(priceRange[0])} - {formatPrice(priceRange[1])}
          </Label>
          <div className="mt-4 px-2">
            <Slider
              value={priceRange}
              onValueChange={handlePriceRangeChange}
              max={20000000}
              min={0}
              step={500000}
              className="w-full"
            />
          </div>
          <div className="mt-2 flex justify-between text-xs text-gray-500">
            <span>$0</span>
            <span>$200k+</span>
          </div>
        </div>

        {/* Range Requirement */}
        <div>
          <Label className="text-sm font-medium">
            Minimum Range: {rangeMin > 0 ? `${rangeMin} miles` : 'Any'}
          </Label>
          <div className="mt-4 px-2">
            <Slider
              value={[rangeMin]}
              onValueChange={handleRangeMinChange}
              max={500}
              min={0}
              step={25}
              className="w-full"
            />
          </div>
          <div className="mt-2 flex justify-between text-xs text-gray-500">
            <span>Any</span>
            <span>500+ miles</span>
          </div>
        </div>

        {/* Production Status */}
        <div>
          <Label className="text-sm font-medium">Availability</Label>
          <Select 
            value={filters.productionStatus || 'current'} 
            onValueChange={handleProductionStatusChange}
          >
            <SelectTrigger className="mt-2">
              <SelectValue placeholder="Production status" />
            </SelectTrigger>
            <SelectContent>
              {PRODUCTION_STATUS.map((status) => (
                <SelectItem key={status} value={status}>
                  {PRODUCTION_STATUS_LABELS[status]}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Special Categories */}
        <div>
          <Label className="text-sm font-medium">Special Categories</Label>
          <div className="mt-2 space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="featured"
                checked={filters.featured === 'true'}
                onCheckedChange={handleFeaturedChange}
              />
              <Label htmlFor="featured" className="text-sm">
                Featured models
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="bestValue"
                checked={filters.bestValue === 'true'}
                onCheckedChange={handleBestValueChange}
              />
              <Label htmlFor="bestValue" className="text-sm">
                Best value picks
              </Label>
            </div>
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div>
            <Label className="text-sm font-medium">Active Filters</Label>
            <div className="mt-2 flex flex-wrap gap-2">
              {filters.make && (
                <Badge variant="secondary" className="text-xs">
                  {filters.make}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onChange({ make: undefined })}
                    className="ml-1 h-auto p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              
              {filters.bodyType && (
                <Badge variant="secondary" className="text-xs">
                  {BODY_TYPE_LABELS[filters.bodyType as EVBodyType]}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onChange({ bodyType: undefined })}
                    className="ml-1 h-auto p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              
              {(filters.priceMin || filters.priceMax) && (
                <Badge variant="secondary" className="text-xs">
                  Price: {formatPrice(parseInt(filters.priceMin || '0'))} - {formatPrice(parseInt(filters.priceMax || '20000000'))}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onChange({ priceMin: undefined, priceMax: undefined })}
                    className="ml-1 h-auto p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              
              {filters.rangeMin && (
                <Badge variant="secondary" className="text-xs">
                  Range: {filters.rangeMin}+ miles
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onChange({ rangeMin: undefined })}
                    className="ml-1 h-auto p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              
              {filters.featured === 'true' && (
                <Badge variant="secondary" className="text-xs">
                  Featured
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onChange({ featured: undefined })}
                    className="ml-1 h-auto p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              
              {filters.bestValue === 'true' && (
                <Badge variant="secondary" className="text-xs">
                  Best Value
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onChange({ bestValue: undefined })}
                    className="ml-1 h-auto p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
