"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ev-models/page",{

/***/ "(app-pages-browser)/./src/components/ev-models/EVModelCard.tsx":
/*!**************************************************!*\
  !*** ./src/components/ev-models/EVModelCard.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EVModelCard: function() { return /* binding */ EVModelCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/battery.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/shared/utils/ev-buyer-guide */ \"(app-pages-browser)/../../packages/shared/src/utils/ev-buyer-guide.ts\");\n/* harmony import */ var _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/shared/constants/ev-buyer-guide */ \"(app-pages-browser)/../../packages/shared/src/constants/ev-buyer-guide.ts\");\n/* __next_internal_client_entry_do_not_use__ EVModelCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EVModelCard(param) {\n    let { model, viewMode, onAddToFavorites, onAddToComparison, isFavorite = false, isInComparison = false } = param;\n    var _model_images, _model_ev_manufacturers;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const primaryImage = ((_model_images = model.images) === null || _model_images === void 0 ? void 0 : _model_images[0]) || _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.ev_model;\n    const manufacturerLogo = ((_model_ev_manufacturers = model.ev_manufacturers) === null || _model_ev_manufacturers === void 0 ? void 0 : _model_ev_manufacturers.logo_url) || _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.manufacturer_logo;\n    const handleAddToFavorites = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        onAddToFavorites === null || onAddToFavorites === void 0 ? void 0 : onAddToFavorites(model.id);\n    };\n    const handleAddToComparison = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        onAddToComparison === null || onAddToComparison === void 0 ? void 0 : onAddToComparison(model.id);\n    };\n    const modelUrl = \"/ev-models/\".concat(model.id);\n    if (viewMode === \"list\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"overflow-hidden transition-shadow hover:shadow-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                href: modelUrl,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative h-32 w-48 shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: imageError ? _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.ev_model : primaryImage,\n                                    alt: \"\".concat(model.make, \" \").concat(model.model),\n                                    fill: true,\n                                    className: \"object-cover\",\n                                    onError: ()=>setImageError(true),\n                                    onLoad: ()=>setImageLoading(false)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 animate-pulse bg-gray-200 dark:bg-gray-700\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-2 top-2 flex flex-col gap-1\",\n                                    children: [\n                                        model.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: \"bg-electric-600 text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Featured\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 19\n                                        }, this),\n                                        model.best_value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            variant: \"secondary\",\n                                            children: \"Best Value\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 38\n                                        }, this),\n                                        model.editor_choice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: \"bg-amber-600 text-white\",\n                                            children: \"Editor's Choice\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 flex-col justify-between p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                manufacturerLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    src: manufacturerLogo,\n                                                                    alt: model.make,\n                                                                    width: 24,\n                                                                    height: 24,\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 100,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: model.make\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 108,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold\",\n                                                            children: [\n                                                                model.model,\n                                                                \" \",\n                                                                model.trim && \"(\".concat(model.trim, \")\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: [\n                                                                model.year,\n                                                                \" • \",\n                                                                model.body_type\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-electric-600\",\n                                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatPrice)(model.price_msrp)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        model.price_base && model.price_base !== model.price_msrp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: [\n                                                                \"From \",\n                                                                (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatPrice)(model.price_base)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 md:grid-cols-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-electric-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatRange)(model.range_epa_miles)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"EPA Range\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 text-electric-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: model.charging_speed_dc_kw ? \"\".concat(model.charging_speed_dc_kw, \"kW\") : \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"DC Charging\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-electric-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatChargingTime)(model.charging_time_10_80_minutes)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"10-80%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-4 text-electric-600\",\n                                                            children: \"⚡\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatAcceleration)(model.acceleration_0_60_mph)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 165,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"0-60 mph\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 168,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: handleAddToFavorites,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"transition-colors\", isFavorite && \"bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-950 dark:text-red-400\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-4 w-4\", isFavorite && \"fill-current\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: handleAddToComparison,\n                                                    disabled: isInComparison,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"transition-colors\", isInComparison && \"dark:bg-electric-950 bg-electric-50 text-electric-600\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        isInComparison ? \"Added\" : \"Compare\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: modelUrl,\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this);\n    }\n    // Grid view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"group overflow-hidden transition-all hover:shadow-lg hover:shadow-electric-600/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n            href: modelUrl,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative aspect-video overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            src: imageError ? _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.ev_model : primaryImage,\n                            alt: \"\".concat(model.make, \" \").concat(model.model),\n                            fill: true,\n                            className: \"object-cover transition-transform group-hover:scale-105\",\n                            onError: ()=>setImageError(true),\n                            onLoad: ()=>setImageLoading(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 animate-pulse bg-gray-200 dark:bg-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-3 top-3 flex flex-col gap-1\",\n                            children: [\n                                model.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    className: \"bg-electric-600 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"mr-1 h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Featured\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, this),\n                                model.best_value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    variant: \"secondary\",\n                                    children: \"Best Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 34\n                                }, this),\n                                model.editor_choice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    className: \"bg-amber-600 text-white\",\n                                    children: \"Editor's Choice\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-3 top-3 flex flex-col gap-2 opacity-0 transition-opacity group-hover:opacity-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAddToFavorites,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-8 w-8 bg-white/90 backdrop-blur-sm hover:bg-white\", isFavorite && \"bg-red-50 text-red-600 hover:bg-red-100\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-4 w-4\", isFavorite && \"fill-current\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAddToComparison,\n                                    disabled: isInComparison,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-8 w-8 bg-white/90 backdrop-blur-sm hover:bg-white\", isInComparison && \"bg-electric-50 text-electric-600\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    className: \"pb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            manufacturerLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: manufacturerLogo,\n                                                alt: model.make,\n                                                width: 20,\n                                                height: 20,\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: model.make\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold leading-tight\",\n                                        children: model.model\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this),\n                                    model.trim && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: model.trim\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-electric-600\",\n                                        children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatPrice)(model.price_msrp)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                        children: model.year\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"pt-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"Range\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatRange)(model.range_epa_miles)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"Charging\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatChargingTime)(model.charging_time_10_80_minutes)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                model.acceleration_0_60_mph && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"0-60 mph\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatAcceleration)(model.acceleration_0_60_mph)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            className: \"mt-4 w-full\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: modelUrl,\n                                children: \"View Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n            lineNumber: 219,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, this);\n}\n_s(EVModelCard, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = EVModelCard;\nvar _c;\n$RefreshReg$(_c, \"EVModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ev-models/EVModelCard.tsx\n"));

/***/ })

});