'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatPrice, formatRange } from '@/shared/utils/ev-buyer-guide'
import type { EVModelWithDetails } from '@/shared/types'

interface EVModelSpecsProps {
  evModel: EVModelWithDetails
}

export function EVModelSpecs({ evModel }: EVModelSpecsProps) {
  return (
    <div className="space-y-8">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Performance Specs */}
        <Card>
          <CardHeader>
            <CardTitle>Performance</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600 dark:text-gray-400">0-60 mph</span>
                <div className="font-semibold">
                  {evModel.acceleration_0_60_mph ? `${evModel.acceleration_0_60_mph}s` : 'N/A'}
                </div>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Top Speed</span>
                <div className="font-semibold">
                  {evModel.top_speed_mph ? `${evModel.top_speed_mph} mph` : 'N/A'}
                </div>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Motor Power</span>
                <div className="font-semibold">
                  {evModel.motor_power_hp ? `${evModel.motor_power_hp} hp` : 'N/A'}
                </div>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Drivetrain</span>
                <div className="font-semibold capitalize">
                  {evModel.drivetrain || 'N/A'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Battery & Charging */}
        <Card>
          <CardHeader>
            <CardTitle>Battery & Charging</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600 dark:text-gray-400">Battery Capacity</span>
                <div className="font-semibold">
                  {evModel.battery_capacity_kwh ? `${evModel.battery_capacity_kwh} kWh` : 'N/A'}
                </div>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">EPA Range</span>
                <div className="font-semibold">
                  {formatRange(evModel.range_epa_miles)}
                </div>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">DC Fast Charging</span>
                <div className="font-semibold">
                  {evModel.charging_speed_dc_kw ? `${evModel.charging_speed_dc_kw} kW` : 'N/A'}
                </div>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">AC Charging</span>
                <div className="font-semibold">
                  {evModel.charging_speed_ac_kw ? `${evModel.charging_speed_ac_kw} kW` : 'N/A'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* More detailed specs would go here */}
      <Card>
        <CardHeader>
          <CardTitle>Complete Specifications</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 dark:text-gray-400">
            Detailed specifications section coming soon...
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
