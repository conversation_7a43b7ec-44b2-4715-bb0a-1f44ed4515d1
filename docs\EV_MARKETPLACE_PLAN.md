# EV Model Database & Listings - Development Plan

## 🎯 Overview

This document outlines the comprehensive plan for implementing a complete database and listings of all existing EV models for the GreenMilesEV platform. This is an informational resource featuring every electric vehicle model available globally, with detailed specifications, comparison tools, and comprehensive search capabilities.

## 🏗️ Architecture Overview

### Core Components

- **EV Model Listings**: Comprehensive database of all existing electric vehicle models
- **EV Model Details**: Detailed specifications and information pages for each model
- **EV Model Comparison**: Side-by-side comparison tool for different EV models
- **Search & Filters**: Advanced search and filtering system for the complete EV database

### Technology Stack

- **Frontend Web**: Next.js 14, TypeScript, Tailwind CSS, shadcn/ui
- **Frontend Mobile**: React Native, Expo, NativeWind
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **Shared**: TypeScript types, utilities, constants

## 📊 Database Schema Design

### New Tables Required

#### `ev_models` Table

```sql
CREATE TABLE ev_models (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  make VARCHAR(50) NOT NULL,
  model VARCHAR(100) NOT NULL,
  year INTEGER NOT NULL,
  trim VARCHAR(100),
  body_type VARCHAR(50), -- sedan, suv, hatchback, truck, etc.
  price_msrp INTEGER, -- MSRP in cents (when available)
  production_status VARCHAR(20) DEFAULT 'current', -- current, discontinued, concept, upcoming
  market_regions TEXT[], -- regions where this model is/was available

  -- Technical Specifications
  battery_capacity_kwh DECIMAL(5,2) NOT NULL,
  range_epa_miles INTEGER,
  range_wltp_miles INTEGER,
  efficiency_mpge INTEGER,
  charging_speed_dc_kw INTEGER,
  charging_speed_ac_kw INTEGER,
  charging_ports TEXT[], -- array of connector types

  -- Performance
  acceleration_0_60_mph DECIMAL(3,1),
  top_speed_mph INTEGER,
  motor_power_hp INTEGER,
  motor_torque_lb_ft INTEGER,
  drivetrain VARCHAR(20), -- fwd, rwd, awd

  -- Physical Specifications
  length_inches DECIMAL(5,1),
  width_inches DECIMAL(5,1),
  height_inches DECIMAL(5,1),
  wheelbase_inches DECIMAL(5,1),
  ground_clearance_inches DECIMAL(3,1),
  cargo_volume_cubic_ft DECIMAL(4,1),
  seating_capacity INTEGER,
  curb_weight_lbs INTEGER,

  -- Features & Technology
  features JSONB, -- structured feature data
  safety_ratings JSONB, -- NHTSA, IIHS ratings
  warranty_info JSONB,

  -- Media & Content
  images TEXT[], -- array of image URLs
  videos TEXT[], -- array of video URLs
  brochure_url TEXT,

  -- Metadata
  manufacturer_id UUID,
  model_year_refresh BOOLEAN DEFAULT false,
  is_featured BOOLEAN DEFAULT false,
  popularity_score INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### `ev_manufacturers` Table

```sql
CREATE TABLE ev_manufacturers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL UNIQUE,
  logo_url TEXT,
  website_url TEXT,
  headquarters_country VARCHAR(50),
  founded_year INTEGER,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### `user_favorites` Table

```sql
CREATE TABLE user_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  ev_model_id UUID REFERENCES ev_models(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, ev_model_id)
);
```

#### `comparison_sessions` Table

```sql
CREATE TABLE comparison_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  name VARCHAR(100),
  ev_model_ids UUID[],
  is_public BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Indexes for Performance

```sql
-- Search and filtering indexes
CREATE INDEX idx_ev_models_make_model ON ev_models(make, model);
CREATE INDEX idx_ev_models_year ON ev_models(year);
CREATE INDEX idx_ev_models_price ON ev_models(price_msrp);
CREATE INDEX idx_ev_models_range ON ev_models(range_epa_miles);
CREATE INDEX idx_ev_models_body_type ON ev_models(body_type);
CREATE INDEX idx_ev_models_production_status ON ev_models(production_status);
CREATE INDEX idx_ev_models_featured ON ev_models(is_featured);

-- Full-text search
CREATE INDEX idx_ev_models_search ON ev_models
USING gin(to_tsvector('english', make || ' ' || model || ' ' || COALESCE(trim, '')));
```

## 🎨 UI/UX Design Specifications

### EV Model Listings Page

- **Grid/List Toggle**: Switch between card grid and detailed list view
- **Advanced Filters**: Price range, range, body type, features, charging speed, production status
- **Sort Options**: Price, range, efficiency, popularity, year, alphabetical
- **Search Bar**: Real-time search with autocomplete across all EV models
- **Pagination**: Load more or infinite scroll
- **Save Search**: Allow users to save filter combinations
- **Model Status**: Clear indicators for current, discontinued, concept, and upcoming models

### EV Model Details Page

- **Hero Section**: Large image gallery with official manufacturer photos
- **Quick Stats**: Key specifications at a glance
- **Tabbed Content**: Specifications, Features, Availability, History
- **Related Models**: Similar EV models and variants
- **Action Buttons**: Add to favorites, Compare, View manufacturer info

### EV Comparison Tool

- **Vehicle Selection**: Search and add up to 4 vehicles
- **Comparison Table**: Side-by-side specifications
- **Visual Charts**: Range, price, efficiency comparisons
- **Export Options**: PDF, share link
- **Highlight Differences**: Visual emphasis on key differences

## 📱 Mobile-Specific Considerations

### Touch-Optimized Interface

- **Swipe Gestures**: Image galleries, comparison cards
- **Pull-to-Refresh**: Update listings data
- **Infinite Scroll**: Smooth loading of more results
- **Bottom Sheets**: Filters and sort options

### Performance Optimizations

- **Image Lazy Loading**: Progressive image loading
- **Virtual Lists**: Efficient rendering of large lists
- **Offline Support**: Cache favorite listings
- **Reduced Data Usage**: Optimized image sizes

## 🔧 Technical Implementation Details

### API Endpoints Design

```typescript
// GET /api/ev-listings
// Query params: page, limit, make, model, year, priceMin, priceMax, etc.

// GET /api/ev-listings/[id]
// Get single EV details

// GET /api/ev-listings/search
// Full-text search with filters

// POST /api/comparisons
// Create comparison session

// GET /api/comparisons/[id]
// Get comparison data
```

### State Management

- **React Query**: Server state management and caching
- **Zustand**: Client state for filters, comparison selections
- **Local Storage**: Persist user preferences and saved searches

### Performance Optimizations

- **Image CDN**: Optimized image delivery
- **Database Indexing**: Fast query performance
- **Caching Strategy**: Redis for frequently accessed data
- **Lazy Loading**: Components and images

## 🧪 Testing Strategy

### Unit Tests

- Utility functions for calculations
- Component rendering and interactions
- API endpoint responses

### Integration Tests

- Search and filter functionality
- Comparison feature workflow
- Mobile navigation and gestures

### E2E Tests

- Complete user journeys
- Cross-platform compatibility
- Performance benchmarks

## 📈 Success Metrics

### User Engagement

- Time spent on listings page
- Click-through rate to details
- Comparison tool usage
- Search query patterns

### Technical Performance

- Page load times < 2s
- Search response time < 500ms
- Mobile app startup time < 3s
- 99.9% uptime

## 🚀 Deployment Plan

### Phase 1: Foundation (Week 1-2)

- Database schema implementation
- Shared types and utilities
- Basic API endpoints

### Phase 2: Web Implementation (Week 3-4)

- EV listings page
- EV details page
- Basic comparison feature

### Phase 3: Mobile Implementation (Week 5-6)

- Mobile listings screen
- Mobile details screen
- Mobile comparison feature

### Phase 4: Enhancement (Week 7-8)

- Advanced search and filters
- Performance optimizations
- Testing and documentation

## 📋 Next Steps

1. **Start with Database Schema**: Implement tables and seed data
2. **Create Shared Types**: Define TypeScript interfaces
3. **Build Web Components**: Start with listings page
4. **Implement Mobile Screens**: Parallel development
5. **Add Advanced Features**: Search, filters, comparison
6. **Testing & Polish**: Comprehensive testing and UX refinement
