'use client'

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import type { EVModelWithDetails } from '@/shared/types'

interface EVModelRealWorldProps {
  evModel: EVModelWithDetails
}

export function EVModelRealWorld({ evModel }: EVModelRealWorldProps) {
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Real-World Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 dark:text-gray-400">
            Real-world performance data and analysis coming soon...
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
