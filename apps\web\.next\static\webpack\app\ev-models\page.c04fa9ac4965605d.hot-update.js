"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ev-models/page",{

/***/ "(app-pages-browser)/./src/components/ev-models/EVModelCard.tsx":
/*!**************************************************!*\
  !*** ./src/components/ev-models/EVModelCard.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EVModelCard: function() { return /* binding */ EVModelCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/battery.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Battery,Clock,Heart,Plus,Star,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/shared/utils/ev-buyer-guide */ \"(app-pages-browser)/../../packages/shared/src/utils/ev-buyer-guide.ts\");\n/* harmony import */ var _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/shared/constants/ev-buyer-guide */ \"(app-pages-browser)/../../packages/shared/src/constants/ev-buyer-guide.ts\");\n/* __next_internal_client_entry_do_not_use__ EVModelCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EVModelCard(param) {\n    let { model, viewMode, onAddToFavorites, isFavorite = false } = param;\n    var _model_images, _model_ev_manufacturers;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const primaryImage = ((_model_images = model.images) === null || _model_images === void 0 ? void 0 : _model_images[0]) || _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.ev_model;\n    const manufacturerLogo = ((_model_ev_manufacturers = model.ev_manufacturers) === null || _model_ev_manufacturers === void 0 ? void 0 : _model_ev_manufacturers.logo_url) || _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.manufacturer_logo;\n    const handleAddToFavorites = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        onAddToFavorites === null || onAddToFavorites === void 0 ? void 0 : onAddToFavorites(model.id);\n    };\n    const handleAddToComparison = (e)=>{\n        var _onAddToComparison;\n        e.preventDefault();\n        e.stopPropagation();\n        (_onAddToComparison = onAddToComparison) === null || _onAddToComparison === void 0 ? void 0 : _onAddToComparison(model.id);\n    };\n    const modelUrl = \"/ev-models/\".concat(model.id);\n    if (viewMode === \"list\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"overflow-hidden transition-shadow hover:shadow-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                href: modelUrl,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative h-32 w-48 shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: imageError ? _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.ev_model : primaryImage,\n                                    alt: \"\".concat(model.make, \" \").concat(model.model),\n                                    fill: true,\n                                    className: \"object-cover\",\n                                    onError: ()=>setImageError(true),\n                                    onLoad: ()=>setImageLoading(false)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this),\n                                imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 animate-pulse bg-gray-200 dark:bg-gray-700\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-2 top-2 flex flex-col gap-1\",\n                                    children: [\n                                        model.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: \"bg-electric-600 text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Featured\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 19\n                                        }, this),\n                                        model.best_value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            variant: \"secondary\",\n                                            children: \"Best Value\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 38\n                                        }, this),\n                                        model.editor_choice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: \"bg-amber-600 text-white\",\n                                            children: \"Editor's Choice\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 flex-col justify-between p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                manufacturerLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    src: manufacturerLogo,\n                                                                    alt: model.make,\n                                                                    width: 24,\n                                                                    height: 24,\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 96,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: model.make\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 104,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold\",\n                                                            children: [\n                                                                model.model,\n                                                                \" \",\n                                                                model.trim && \"(\".concat(model.trim, \")\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: [\n                                                                model.year,\n                                                                \" • \",\n                                                                model.body_type\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-electric-600\",\n                                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatPrice)(model.price_msrp)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        model.price_base && model.price_base !== model.price_msrp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: [\n                                                                \"From \",\n                                                                (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatPrice)(model.price_base)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 md:grid-cols-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-electric-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatRange)(model.range_epa_miles)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"EPA Range\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 134,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 text-electric-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: model.charging_speed_dc_kw ? \"\".concat(model.charging_speed_dc_kw, \"kW\") : \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"DC Charging\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-electric-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatChargingTime)(model.charging_time_10_80_minutes)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 151,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"10-80%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-4 text-electric-600\",\n                                                            children: \"⚡\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatAcceleration)(model.acceleration_0_60_mph)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: \"0-60 mph\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: handleAddToFavorites,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"transition-colors\", isFavorite && \"bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-950 dark:text-red-400\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-4 w-4\", isFavorite && \"fill-current\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: handleAddToComparison,\n                                                    disabled: isInComparison,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"transition-colors\", isInComparison && \"dark:bg-electric-950 bg-electric-50 text-electric-600\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        isInComparison ? \"Added\" : \"Compare\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: modelUrl,\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this);\n    }\n    // Grid view\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"group overflow-hidden transition-all hover:shadow-lg hover:shadow-electric-600/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n            href: modelUrl,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative aspect-video overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            src: imageError ? _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_9__.PLACEHOLDER_IMAGES.ev_model : primaryImage,\n                            alt: \"\".concat(model.make, \" \").concat(model.model),\n                            fill: true,\n                            className: \"object-cover transition-transform group-hover:scale-105\",\n                            onError: ()=>setImageError(true),\n                            onLoad: ()=>setImageLoading(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 animate-pulse bg-gray-200 dark:bg-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-3 top-3 flex flex-col gap-1\",\n                            children: [\n                                model.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    className: \"bg-electric-600 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"mr-1 h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Featured\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this),\n                                model.best_value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    variant: \"secondary\",\n                                    children: \"Best Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 34\n                                }, this),\n                                model.editor_choice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                    className: \"bg-amber-600 text-white\",\n                                    children: \"Editor's Choice\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-3 top-3 flex flex-col gap-2 opacity-0 transition-opacity group-hover:opacity-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAddToFavorites,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-8 w-8 bg-white/90 backdrop-blur-sm hover:bg-white\", isFavorite && \"bg-red-50 text-red-600 hover:bg-red-100\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-4 w-4\", isFavorite && \"fill-current\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: handleAddToComparison,\n                                    disabled: isInComparison,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-8 w-8 bg-white/90 backdrop-blur-sm hover:bg-white\", isInComparison && \"bg-electric-50 text-electric-600\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Battery_Clock_Heart_Plus_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    className: \"pb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            manufacturerLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: manufacturerLogo,\n                                                alt: model.make,\n                                                width: 20,\n                                                height: 20,\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: model.make\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold leading-tight\",\n                                        children: model.model\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    model.trim && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: model.trim\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-electric-600\",\n                                        children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatPrice)(model.price_msrp)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                        children: model.year\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"pt-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"Range\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatRange)(model.range_epa_miles)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"Charging\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatChargingTime)(model.charging_time_10_80_minutes)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                model.acceleration_0_60_mph && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"0-60 mph\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_8__.formatAcceleration)(model.acceleration_0_60_mph)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            className: \"mt-4 w-full\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: modelUrl,\n                                children: \"View Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ev-models\\\\EVModelCard.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(EVModelCard, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = EVModelCard;\nvar _c;\n$RefreshReg$(_c, \"EVModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ev-models/EVModelCard.tsx\n"));

/***/ })

});