"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ev-models/[id]/page",{

/***/ "(app-pages-browser)/./src/components/comparison/ComparisonToggle.tsx":
/*!********************************************************!*\
  !*** ./src/components/comparison/ComparisonToggle.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComparisonToggle: function() { return /* binding */ ComparisonToggle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ComparisonContext */ \"(app-pages-browser)/./src/contexts/ComparisonContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ ComparisonToggle auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ComparisonToggle(param) {\n    let { className } = param;\n    _s();\n    const { state, togglePanel } = (0,_contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_4__.useComparison)();\n    if (state.models.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n        variant: state.isOpen ? \"default\" : \"outline\",\n        onClick: togglePanel,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative transition-colors\", state.isOpen && \"bg-electric-600 text-white hover:bg-electric-700\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Compare, {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonToggle.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            \"Compare\",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                variant: state.isOpen ? \"secondary\" : \"default\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"ml-2 h-5 w-5 rounded-full p-0 text-xs\", state.isOpen && \"bg-white text-electric-600\"),\n                children: state.models.length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonToggle.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonToggle.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(ComparisonToggle, \"ms7d95nhrTquxY89QuzAxp1j4oo=\", false, function() {\n    return [\n        _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_4__.useComparison\n    ];\n});\n_c = ComparisonToggle;\nvar _c;\n$RefreshReg$(_c, \"ComparisonToggle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/comparison/ComparisonToggle.tsx\n"));

/***/ })

});