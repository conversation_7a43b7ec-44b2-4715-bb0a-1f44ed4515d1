/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ev-models/[id]/route";
exports.ids = ["app/api/ev-models/[id]/route"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!****************************************************************!*\
  !*** ../../node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \****************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fev-models%2F%5Bid%5D%2Froute&page=%2Fapi%2Fev-models%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fev-models%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fev-models%2F%5Bid%5D%2Froute&page=%2Fapi%2Fev-models%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fev-models%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_obi_c_Desktop_ev_app_apps_web_src_app_api_ev_models_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ev-models/[id]/route.ts */ \"(rsc)/./src/app/api/ev-models/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ev-models/[id]/route\",\n        pathname: \"/api/ev-models/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/ev-models/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\api\\\\ev-models\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_obi_c_Desktop_ev_app_apps_web_src_app_api_ev_models_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/ev-models/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fev-models%2F%5Bid%5D%2Froute&page=%2Fapi%2Fev-models%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fev-models%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/ev-models/[id]/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/ev-models/[id]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://pbevpexclffmhqstwlha.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZXZwZXhjbGZmbWhxc3R3bGhhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjA1MzksImV4cCI6MjA2NTQ5NjUzOX0.F6bMKoeKV5QTyt0oEfNDn6-s780wkvvddplEBUuRpsI\");\nasync function GET(request, { params }) {\n    try {\n        const { id } = params;\n        // Validate UUID format\n        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;\n        if (!uuidRegex.test(id)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid EV model ID format\"\n            }, {\n                status: 400\n            });\n        }\n        // Fetch the EV model with all details\n        const { data: evModel, error } = await supabase.from(\"ev_models\").select(`\n        *,\n        ev_manufacturers!inner(\n          id,\n          name,\n          logo_url,\n          website_url,\n          headquarters_country,\n          founded_year,\n          description\n        )\n      `).eq(\"id\", id).single();\n        if (error) {\n            if (error.code === \"PGRST116\") {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"EV model not found\"\n                }, {\n                    status: 404\n                });\n            }\n            console.error(\"Error fetching EV model:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to fetch EV model\"\n            }, {\n                status: 500\n            });\n        }\n        // Fetch similar models (same body type, similar price range)\n        const priceRange = evModel.price_msrp ? evModel.price_msrp * 0.2 : 1000000 // 20% price range\n        ;\n        const { data: similarModels } = await supabase.from(\"ev_models\").select(`\n        id,\n        make,\n        model,\n        year,\n        trim,\n        price_msrp,\n        range_epa_miles,\n        images,\n        is_featured,\n        best_value,\n        ev_manufacturers!inner(name, logo_url)\n      `).eq(\"body_type\", evModel.body_type).neq(\"id\", id).eq(\"production_status\", \"current\").gte(\"price_msrp\", evModel.price_msrp ? evModel.price_msrp - priceRange : 0).lte(\"price_msrp\", evModel.price_msrp ? evModel.price_msrp + priceRange : 999999999).order(\"popularity_score\", {\n            ascending: false\n        }).limit(6);\n        // Calculate some buyer-focused metrics\n        const buyerMetrics = {\n            costPerMile: evModel.price_msrp && evModel.range_epa_miles ? Math.round(evModel.price_msrp / 100 / evModel.range_epa_miles) : null,\n            chargingEfficiency: evModel.charging_speed_dc_kw && evModel.battery_capacity_kwh ? Math.round(evModel.charging_speed_dc_kw / evModel.battery_capacity_kwh * 100) / 100 : null,\n            practicalRange: evModel.range_real_world_miles || (evModel.range_epa_miles ? Math.round(evModel.range_epa_miles * 0.85) : null),\n            powerToWeightRatio: evModel.motor_power_hp && evModel.curb_weight_lbs ? Math.round(evModel.motor_power_hp / evModel.curb_weight_lbs * 1000) / 1000 : null\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data: {\n                ...evModel,\n                buyerMetrics,\n                similarModels: similarModels || []\n            }\n        });\n    } catch (error) {\n        console.error(\"Unexpected error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ev-models/[id]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fev-models%2F%5Bid%5D%2Froute&page=%2Fapi%2Fev-models%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fev-models%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();