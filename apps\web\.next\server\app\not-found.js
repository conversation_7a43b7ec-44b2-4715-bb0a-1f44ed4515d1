/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!****************************************************************!*\
  !*** ../../node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \****************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2F..%2F..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2F..%2F..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?64b7\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/../../node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2F..%2F..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5Ccomparison%5CComparisonPanel.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CThemeProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CToastProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CComparisonContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5Ccomparison%5CComparisonPanel.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CThemeProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CToastProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CComparisonContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AuthErrorBoundary.tsx */ \"(ssr)/./src/components/AuthErrorBoundary.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/comparison/ComparisonPanel.tsx */ \"(ssr)/./src/components/comparison/ComparisonPanel.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ToastProvider.tsx */ \"(ssr)/./src/components/ToastProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ComparisonContext.tsx */ \"(ssr)/./src/contexts/ComparisonContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5Ccomparison%5CComparisonPanel.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CThemeProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CToastProvider.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CComparisonContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthErrorBoundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/AuthErrorBoundary.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthErrorBoundary: () => (/* binding */ AuthErrorBoundary),\n/* harmony export */   useAuthErrorHandler: () => (/* binding */ useAuthErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ AuthErrorBoundary,useAuthErrorHandler auto */ \n\n\n\n\nclass AuthErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.retry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"Authentication error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                error: this.state.error,\n                retry: this.retry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                lineNumber: 42,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, retry }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.AlertTitle, {\n                            children: \"Authentication Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.AlertDescription, {\n                            className: \"mt-2\",\n                            children: error.message || \"An unexpected error occurred during authentication.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: retry,\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>window.location.href = \"/\",\n                            className: \"flex-1\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n// Hook for handling auth errors in functional components\nfunction useAuthErrorHandler() {\n    const handleAuthError = (error)=>{\n        console.error(\"Auth error:\", error);\n        // Handle specific error types\n        if (error?.message?.includes(\"Invalid login credentials\")) {\n            return \"Invalid email or password. Please check your credentials and try again.\";\n        }\n        if (error?.message?.includes(\"Email not confirmed\")) {\n            return \"Please check your email and click the confirmation link before signing in.\";\n        }\n        if (error?.message?.includes(\"Too many requests\")) {\n            return \"Too many login attempts. Please wait a few minutes before trying again.\";\n        }\n        if (error?.message?.includes(\"Network\")) {\n            return \"Network error. Please check your internet connection and try again.\";\n        }\n        // Default error message\n        return error?.message || \"An unexpected error occurred. Please try again.\";\n    };\n    return {\n        handleAuthError\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL0BncmVlbm1pbGVzLWV2L3dlYi8uL3NyYy9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4PzZmNDciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIgfSBmcm9tICduZXh0LXRoZW1lcydcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSAnbmV4dC10aGVtZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ToastProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ToastProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider auto */ \n\nfunction ToastProvider() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        position: \"top-right\",\n        toastOptions: {\n            // Default options\n            duration: 4000,\n            style: {\n                background: \"var(--background)\",\n                color: \"var(--foreground)\",\n                border: \"1px solid var(--border)\"\n            },\n            // Success toast styling\n            success: {\n                duration: 3000,\n                style: {\n                    background: \"rgb(34 197 94)\",\n                    color: \"white\",\n                    border: \"1px solid rgb(34 197 94)\"\n                },\n                iconTheme: {\n                    primary: \"white\",\n                    secondary: \"rgb(34 197 94)\"\n                }\n            },\n            // Error toast styling\n            error: {\n                duration: 5000,\n                style: {\n                    background: \"rgb(239 68 68)\",\n                    color: \"white\",\n                    border: \"1px solid rgb(239 68 68)\"\n                },\n                iconTheme: {\n                    primary: \"white\",\n                    secondary: \"rgb(239 68 68)\"\n                }\n            },\n            // Loading toast styling\n            loading: {\n                style: {\n                    background: \"rgb(59 130 246)\",\n                    color: \"white\",\n                    border: \"1px solid rgb(59 130 246)\"\n                },\n                iconTheme: {\n                    primary: \"white\",\n                    secondary: \"rgb(59 130 246)\"\n                }\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ToastProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ToastProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/comparison/ComparisonPanel.tsx":
/*!*******************************************************!*\
  !*** ./src/components/comparison/ComparisonPanel.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComparisonPanel: () => (/* binding */ ComparisonPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/../../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Compare,ExternalLink,Trash2,X!=!lucide-react */ \"(ssr)/__barrel_optimize__?names=ChevronDown,ChevronUp,Compare,ExternalLink,Trash2,X!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Compare,ExternalLink,Trash2,X!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Compare,ExternalLink,Trash2,X!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Compare,ExternalLink,Trash2,X!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Compare,ExternalLink,Trash2,X!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Compare,ExternalLink,Trash2,X!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/ComparisonContext */ \"(ssr)/./src/contexts/ComparisonContext.tsx\");\n/* harmony import */ var _hooks_useComparisonActions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useComparisonActions */ \"(ssr)/./src/hooks/useComparisonActions.ts\");\n/* harmony import */ var _shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/shared/utils/ev-buyer-guide */ \"(ssr)/../../packages/shared/src/utils/ev-buyer-guide.ts\");\n/* harmony import */ var _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/shared/constants/ev-buyer-guide */ \"(ssr)/../../packages/shared/src/constants/ev-buyer-guide.ts\");\n/* __next_internal_client_entry_do_not_use__ ComparisonPanel auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction ComparisonPanel() {\n    const { state, togglePanel, closePanel } = (0,_contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_8__.useComparison)();\n    const { removeFromComparison, clearComparison } = (0,_hooks_useComparisonActions__WEBPACK_IMPORTED_MODULE_9__.useComparisonActions)();\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (state.models.length === 0) {\n        return null;\n    }\n    const handleToggleMinimize = ()=>{\n        setIsMinimized(!isMinimized);\n    };\n    const handleCompareAll = ()=>{\n        // Navigate to comparison page with all models\n        const modelIds = state.models.map((m)=>m.id).join(\",\");\n        window.open(`/compare?models=${modelIds}`, \"_blank\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)] transition-all duration-300\", state.isOpen ? \"translate-y-0\" : \"translate-y-full\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"shadow-2xl border-electric-200 dark:border-electric-800\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    className: \"pb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"flex items-center gap-2 text-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Compare, {\n                                        className: \"h-5 w-5 text-electric-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Compare EVs\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"ml-2\",\n                                        children: [\n                                            state.models.length,\n                                            \"/4\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleToggleMinimize,\n                                        className: \"h-8 w-8 p-0\",\n                                        children: isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: closePanel,\n                                        className: \"h-8 w-8 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 max-h-64 overflow-y-auto\",\n                            children: state.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 rounded-lg border p-3 hover:bg-gray-50 dark:hover:bg-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-12 w-16 shrink-0 overflow-hidden rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: model.images?.[0] || _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_11__.PLACEHOLDER_IMAGES.ev_model,\n                                                alt: `${model.make} ${model.model}`,\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-sm truncate\",\n                                                    children: [\n                                                        model.make,\n                                                        \" \",\n                                                        model.model\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: [\n                                                        (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_10__.formatPrice)(model.price_msrp),\n                                                        \" • \",\n                                                        (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_10__.formatRange)(model.range_epa_miles)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    className: \"h-8 w-8 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: `/ev-models/${model.id}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>removeFromComparison(model.id),\n                                                    className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, model.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-2 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleCompareAll,\n                                    className: \"flex-1\",\n                                    disabled: state.models.length < 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Compare, {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Compare All\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: clearComparison,\n                                    className: \"text-red-600 hover:text-red-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Compare_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        state.models.length < 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-600 dark:text-gray-400 text-center\",\n                            children: \"Add at least 2 models to start comparing\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/comparison/ComparisonPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n            if (error) {\n                console.error(\"Error getting session:\", error);\n            }\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchProfile();\n            }\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state changed:\", event, session?.user?.email);\n            setSession(session);\n            setUser(session?.user ?? null);\n            try {\n                if (session?.user) {\n                    await fetchProfile();\n                } else {\n                    setProfile(null);\n                }\n            } catch (error) {\n                console.error(\"Error in auth state change handler:\", error);\n            } finally{\n                // Always set loading to false after handling auth state change\n                setLoading(false);\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const fetchProfile = async ()=>{\n        try {\n            console.log(\"Fetching user profile...\");\n            // Add timeout to prevent hanging\n            const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Profile fetch timeout\")), 10000));\n            const profilePromise = _lib_api__WEBPACK_IMPORTED_MODULE_3__.profileApi.getProfile();\n            const { data, error } = await Promise.race([\n                profilePromise,\n                timeoutPromise\n            ]);\n            if (error) {\n                console.error(\"Error fetching profile:\", error);\n            // Don't throw error - just log it and continue\n            } else {\n                console.log(\"Profile fetched successfully:\", data);\n                setProfile(data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n        // Don't throw error - just log it and continue\n        // This prevents the auth flow from hanging if profile fetch fails\n        }\n    };\n    const signUp = async (email, password, metadata)=>{\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: metadata\n                }\n            });\n            return {\n                data,\n                error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            console.log(\"Attempting sign in for:\", email);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                console.error(\"Sign in error:\", error);\n                setLoading(false) // Only set loading false on error\n                ;\n            } else {\n                console.log(\"Sign in successful, waiting for auth state change...\");\n            // Don't set loading false here - let the auth state change handler do it\n            }\n            return {\n                data,\n                error\n            };\n        } catch (err) {\n            console.error(\"Sign in exception:\", err);\n            setLoading(false);\n            throw err;\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            return {\n                error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetPassword = async (email)=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/reset-password`\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const signInWithGoogle = async ()=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n            provider: \"google\",\n            options: {\n                redirectTo: `${window.location.origin}/dashboard`\n            }\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const signInWithGitHub = async ()=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n            provider: \"github\",\n            options: {\n                redirectTo: `${window.location.origin}/dashboard`\n            }\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const updateProfile = async (updates)=>{\n        try {\n            const { data, error } = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.profileApi.updateProfile(updates);\n            if (data && !error) {\n                setProfile(data);\n            }\n            return {\n                data,\n                error\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    };\n    const refreshProfile = async ()=>{\n        await fetchProfile();\n    };\n    const value = {\n        user,\n        profile,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        signInWithGoogle,\n        signInWithGitHub,\n        updateProfile,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 224,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Higher-order component for protected routes\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { user, loading } = useAuth();\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex min-h-screen items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 w-32 animate-spin rounded-full border-b-2 border-electric-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            // Redirect to login page\n            window.location.href = \"/auth/signin\";\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 254,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ComparisonContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/ComparisonContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComparisonProvider: () => (/* binding */ ComparisonProvider),\n/* harmony export */   MAX_COMPARISON_MODELS: () => (/* binding */ MAX_COMPARISON_MODELS),\n/* harmony export */   useComparison: () => (/* binding */ useComparison)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ComparisonProvider,useComparison,MAX_COMPARISON_MODELS auto */ \n\n// Maximum number of models that can be compared at once\nconst MAX_COMPARISON_MODELS = 4;\n// Initial state\nconst initialState = {\n    models: [],\n    isOpen: false,\n    maxReached: false\n};\n// Comparison reducer\nfunction comparisonReducer(state, action) {\n    switch(action.type){\n        case \"ADD_MODEL\":\n            {\n                // Don't add if already exists or max reached\n                if (state.models.some((model)=>model.id === action.payload.id)) {\n                    return state;\n                }\n                if (state.models.length >= MAX_COMPARISON_MODELS) {\n                    return {\n                        ...state,\n                        maxReached: true\n                    };\n                }\n                const newModels = [\n                    ...state.models,\n                    action.payload\n                ];\n                return {\n                    ...state,\n                    models: newModels,\n                    maxReached: newModels.length >= MAX_COMPARISON_MODELS\n                };\n            }\n        case \"REMOVE_MODEL\":\n            {\n                const newModels = state.models.filter((model)=>model.id !== action.payload);\n                return {\n                    ...state,\n                    models: newModels,\n                    maxReached: false,\n                    isOpen: newModels.length > 0 ? state.isOpen : false\n                };\n            }\n        case \"CLEAR_ALL\":\n            return {\n                ...state,\n                models: [],\n                maxReached: false,\n                isOpen: false\n            };\n        case \"TOGGLE_PANEL\":\n            return {\n                ...state,\n                isOpen: state.models.length > 0 ? !state.isOpen : false\n            };\n        case \"OPEN_PANEL\":\n            return {\n                ...state,\n                isOpen: state.models.length > 0\n            };\n        case \"CLOSE_PANEL\":\n            return {\n                ...state,\n                isOpen: false\n            };\n        case \"LOAD_FROM_STORAGE\":\n            return {\n                ...state,\n                models: action.payload.slice(0, MAX_COMPARISON_MODELS),\n                maxReached: action.payload.length >= MAX_COMPARISON_MODELS\n            };\n        default:\n            return state;\n    }\n}\n// Create context\nconst ComparisonContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Local storage key\nconst STORAGE_KEY = \"ev-comparison-models\";\nfunction ComparisonProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(comparisonReducer, initialState);\n    // Load from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const stored = localStorage.getItem(STORAGE_KEY);\n            if (stored) {\n                const models = JSON.parse(stored);\n                dispatch({\n                    type: \"LOAD_FROM_STORAGE\",\n                    payload: models\n                });\n            }\n        } catch (error) {\n            console.error(\"Failed to load comparison models from storage:\", error);\n        }\n    }, []);\n    // Save to localStorage whenever models change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(state.models));\n        } catch (error) {\n            console.error(\"Failed to save comparison models to storage:\", error);\n        }\n    }, [\n        state.models\n    ]);\n    // Action creators\n    const addModel = (model)=>{\n        dispatch({\n            type: \"ADD_MODEL\",\n            payload: model\n        });\n    };\n    const removeModel = (modelId)=>{\n        dispatch({\n            type: \"REMOVE_MODEL\",\n            payload: modelId\n        });\n    };\n    const clearAll = ()=>{\n        dispatch({\n            type: \"CLEAR_ALL\"\n        });\n    };\n    const togglePanel = ()=>{\n        dispatch({\n            type: \"TOGGLE_PANEL\"\n        });\n    };\n    const openPanel = ()=>{\n        dispatch({\n            type: \"OPEN_PANEL\"\n        });\n    };\n    const closePanel = ()=>{\n        dispatch({\n            type: \"CLOSE_PANEL\"\n        });\n    };\n    const isModelInComparison = (modelId)=>{\n        return state.models.some((model)=>model.id === modelId);\n    };\n    const canAddMore = state.models.length < MAX_COMPARISON_MODELS;\n    const comparisonCount = state.models.length;\n    const contextValue = {\n        state,\n        addModel,\n        removeModel,\n        clearAll,\n        togglePanel,\n        openPanel,\n        closePanel,\n        isModelInComparison,\n        canAddMore,\n        comparisonCount\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComparisonContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\ComparisonContext.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n// Custom hook to use comparison context\nfunction useComparison() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ComparisonContext);\n    if (context === undefined) {\n        throw new Error(\"useComparison must be used within a ComparisonProvider\");\n    }\n    return context;\n}\n// Export constants for use in other components\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ComparisonContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useComparisonActions.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useComparisonActions.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComparisonActions: () => (/* binding */ useComparisonActions)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/ComparisonContext */ \"(ssr)/./src/contexts/ComparisonContext.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/../../node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ useComparisonActions auto */ \n\n\n/**\n * Custom hook that provides comparison actions with user feedback\n */ function useComparisonActions() {\n    const { addModel, removeModel, clearAll, isModelInComparison, canAddMore, comparisonCount, state } = (0,_contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_1__.useComparison)();\n    const handleAddToComparison = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((model)=>{\n        if (isModelInComparison(model.id)) {\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.info(`${model.make} ${model.model} is already in comparison`);\n            return false;\n        }\n        if (!canAddMore) {\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"Maximum 4 models can be compared at once\");\n            return false;\n        }\n        addModel(model);\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(`Added ${model.make} ${model.model} to comparison`);\n        return true;\n    }, [\n        addModel,\n        isModelInComparison,\n        canAddMore\n    ]);\n    const handleRemoveFromComparison = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((modelId)=>{\n        const model = state.models.find((m)=>m.id === modelId);\n        removeModel(modelId);\n        if (model) {\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(`Removed ${model.make} ${model.model} from comparison`);\n        }\n    }, [\n        removeModel,\n        state.models\n    ]);\n    const handleClearComparison = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (comparisonCount === 0) {\n            return;\n        }\n        clearAll();\n        sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Cleared all models from comparison\");\n    }, [\n        clearAll,\n        comparisonCount\n    ]);\n    const handleToggleComparison = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((model)=>{\n        if (isModelInComparison(model.id)) {\n            handleRemoveFromComparison(model.id);\n        } else {\n            handleAddToComparison(model);\n        }\n    }, [\n        isModelInComparison,\n        handleAddToComparison,\n        handleRemoveFromComparison\n    ]);\n    return {\n        addToComparison: handleAddToComparison,\n        removeFromComparison: handleRemoveFromComparison,\n        clearComparison: handleClearComparison,\n        toggleComparison: handleToggleComparison,\n        isInComparison: isModelInComparison,\n        canAddMore,\n        comparisonCount,\n        comparisonModels: state.models\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useComparisonActions.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsApi: () => (/* binding */ analyticsApi),\n/* harmony export */   chargingSessionApi: () => (/* binding */ chargingSessionApi),\n/* harmony export */   chargingStationApi: () => (/* binding */ chargingStationApi),\n/* harmony export */   maintenanceApi: () => (/* binding */ maintenanceApi),\n/* harmony export */   profileApi: () => (/* binding */ profileApi),\n/* harmony export */   tripApi: () => (/* binding */ tripApi),\n/* harmony export */   vehicleApi: () => (/* binding */ vehicleApi)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\n// Profile API\nconst profileApi = {\n    async getProfile () {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            return {\n                data: null,\n                error: {\n                    message: \"No authenticated user\"\n                }\n            };\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateProfile (updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", (await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser()).data.user?.id).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async uploadAvatar (file) {\n        try {\n            const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (!user) throw new Error(\"No user found\");\n            const fileExt = file.name.split(\".\").pop();\n            const fileName = `${user.id}-${Math.random()}.${fileExt}`;\n            const filePath = `avatars/${fileName}`;\n            // Upload file to storage\n            const { data: uploadData, error: uploadError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(\"avatars\").upload(filePath, file, {\n                cacheControl: \"3600\",\n                upsert: false\n            });\n            if (uploadError) throw uploadError;\n            // Get public URL\n            const { data: { publicUrl } } = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(\"avatars\").getPublicUrl(filePath);\n            // Update profile with new avatar URL\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").update({\n                avatar_url: publicUrl,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            return {\n                data,\n                error\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    },\n    async deleteAvatar () {\n        try {\n            const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (!user) throw new Error(\"No user found\");\n            // Get current profile to find avatar URL\n            const { data: profile } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").select(\"avatar_url\").eq(\"id\", user.id).single();\n            // Delete file from storage if it exists\n            if (profile?.avatar_url) {\n                const fileName = profile.avatar_url.split(\"/\").pop();\n                if (fileName) {\n                    await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(\"avatars\").remove([\n                        `avatars/${fileName}`\n                    ]);\n                }\n            }\n            // Update profile to remove avatar URL\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").update({\n                avatar_url: null,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            return {\n                data,\n                error\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    },\n    async getUserStats () {\n        try {\n            const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (!user) throw new Error(\"No user found\");\n            // Get vehicle count\n            const { count: vehicleCount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"user_id\", user.id);\n            // Get charging session count\n            const { count: chargingSessionCount } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"user_id\", user.id);\n            // Get total miles from trips\n            const { data: tripsData } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(\"distance_miles\").eq(\"user_id\", user.id);\n            const totalMiles = tripsData?.reduce((sum, trip)=>sum + (trip.distance_miles || 0), 0) || 0;\n            return {\n                data: {\n                    vehicleCount: vehicleCount || 0,\n                    chargingSessionCount: chargingSessionCount || 0,\n                    totalMiles: Math.round(totalMiles)\n                },\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: {\n                    vehicleCount: 0,\n                    chargingSessionCount: 0,\n                    totalMiles: 0\n                },\n                error\n            };\n        }\n    }\n};\n// Vehicle API\nconst vehicleApi = {\n    async getVehicles () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        return {\n            data,\n            error\n        };\n    },\n    async getVehicle (id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\").eq(\"id\", id).single();\n        return {\n            data,\n            error\n        };\n    },\n    async createVehicle (vehicle) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").insert([\n            vehicle\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateVehicle (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async deleteVehicle (id) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").delete().eq(\"id\", id);\n        return {\n            error\n        };\n    }\n};\n// Charging Station API\nconst chargingStationApi = {\n    async getChargingStations (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_stations\").select(\"*\").eq(\"status\", \"operational\").limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async getNearbyStations (latitude, longitude, radiusMiles = 25) {\n        // Using PostGIS extension for geographic queries (if available)\n        // For now, we'll fetch all stations and filter client-side\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_stations\").select(\"*\").eq(\"status\", \"operational\");\n        if (data && !error) {\n            // Simple distance calculation (for production, use PostGIS)\n            const stationsWithDistance = data.map((station)=>({\n                    ...station,\n                    distance: calculateDistance(latitude, longitude, station.latitude, station.longitude)\n                })).filter((station)=>station.distance <= radiusMiles).sort((a, b)=>a.distance - b.distance);\n            return {\n                data: stationsWithDistance,\n                error\n            };\n        }\n        return {\n            data,\n            error\n        };\n    }\n};\n// Charging Session API\nconst chargingSessionApi = {\n    async getChargingSessions (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(`\n        *,\n        vehicle:vehicles(make, model, year),\n        charging_station:charging_stations(name, network, address)\n      `).order(\"start_time\", {\n            ascending: false\n        }).limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async createChargingSession (session) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").insert([\n            session\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateChargingSession (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Trip API\nconst tripApi = {\n    async getTrips (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(`\n        *,\n        vehicle:vehicles(make, model, year)\n      `).order(\"start_time\", {\n            ascending: false\n        }).limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async createTrip (trip) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").insert([\n            trip\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateTrip (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Maintenance API\nconst maintenanceApi = {\n    async getMaintenanceRecords (vehicleId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").select(`\n        *,\n        vehicle:vehicles(make, model, year)\n      `).order(\"service_date\", {\n            ascending: false\n        });\n        if (vehicleId) {\n            query = query.eq(\"vehicle_id\", vehicleId);\n        }\n        const { data, error } = await query;\n        return {\n            data,\n            error\n        };\n    },\n    async createMaintenanceRecord (record) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").insert([\n            record\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateMaintenanceRecord (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Analytics API\nconst analyticsApi = {\n    async getVehicleAnalytics (vehicleId, startDate, endDate) {\n        // Get trips for the vehicle\n        let tripsQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(\"*\").eq(\"vehicle_id\", vehicleId).eq(\"status\", \"completed\");\n        if (startDate) tripsQuery = tripsQuery.gte(\"start_time\", startDate);\n        if (endDate) tripsQuery = tripsQuery.lte(\"start_time\", endDate);\n        const { data: trips, error: tripsError } = await tripsQuery;\n        // Get charging sessions for the vehicle\n        let sessionsQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(\"*\").eq(\"vehicle_id\", vehicleId).eq(\"status\", \"completed\");\n        if (startDate) sessionsQuery = sessionsQuery.gte(\"start_time\", startDate);\n        if (endDate) sessionsQuery = sessionsQuery.lte(\"start_time\", endDate);\n        const { data: sessions, error: sessionsError } = await sessionsQuery;\n        if (tripsError || sessionsError) {\n            return {\n                data: null,\n                error: tripsError || sessionsError\n            };\n        }\n        // Calculate analytics\n        const totalMiles = trips?.reduce((sum, trip)=>sum + (trip.distance_miles || 0), 0) || 0;\n        const totalTrips = trips?.length || 0;\n        const totalEnergyUsed = trips?.reduce((sum, trip)=>sum + (trip.energy_used_kwh || 0), 0) || 0;\n        const totalChargingCost = sessions?.reduce((sum, session)=>sum + (session.cost_total || 0), 0) || 0;\n        const averageEfficiency = totalEnergyUsed > 0 ? totalMiles / totalEnergyUsed : 0;\n        // Estimate carbon and money savings (compared to average gas car)\n        const carbonSavedLbs = totalMiles * 0.89 // Rough estimate: 0.89 lbs CO2 per mile for gas car\n        ;\n        const moneySaved = totalMiles / 25 * 3.5 - totalChargingCost // Assume 25 MPG, $3.50/gallon\n        ;\n        const analytics = {\n            total_miles: Math.round(totalMiles * 10) / 10,\n            total_trips: totalTrips,\n            average_efficiency: Math.round(averageEfficiency * 100) / 100,\n            total_energy_used: Math.round(totalEnergyUsed * 10) / 10,\n            total_charging_cost: Math.round(totalChargingCost * 100) / 100,\n            carbon_saved_lbs: Math.round(carbonSavedLbs * 10) / 10,\n            money_saved: Math.round(moneySaved * 100) / 100\n        };\n        return {\n            data: analytics,\n            error: null\n        };\n    }\n};\n// Helper function for distance calculation\nfunction calculateDistance(lat1, lon1, lat2, lon2) {\n    const R = 3959 // Earth's radius in miles\n    ;\n    const dLat = toRadians(lat2 - lat1);\n    const dLon = toRadians(lon2 - lon1);\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n}\nfunction toRadians(degrees) {\n    return degrees * (Math.PI / 180);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://pbevpexclffmhqstwlha.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZXZwZXhjbGZmbWhxc3R3bGhhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjA1MzksImV4cCI6MjA2NTQ5NjUzOX0.F6bMKoeKV5QTyt0oEfNDn6-s780wkvvddplEBUuRpsI\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBR3BELE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUVqRSxJQUFJLENBQUNELGVBQWUsQ0FBQ0ksaUJBQWlCO0lBQ3BDLE1BQU0sSUFBSUUsTUFBTTtBQUNsQjtBQUVPLE1BQU1DLFdBQVdSLG1FQUFZQSxDQUFXQyxhQUFhSSxpQkFBaUI7SUFDM0VJLE1BQU07UUFDSkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLG9CQUFvQjtJQUN0QjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZ3JlZW5taWxlcy1ldi93ZWIvLi9zcmMvbGliL3N1cGFiYXNlLnRzPzA2ZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuaW1wb3J0IHR5cGUgeyBEYXRhYmFzZSB9IGZyb20gJ0Avc2hhcmVkL3R5cGVzJ1xuXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCFcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuXG5pZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUFub25LZXkpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIFN1cGFiYXNlIGVudmlyb25tZW50IHZhcmlhYmxlcycpXG59XG5cbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudDxEYXRhYmFzZT4oc3VwYWJhc2VVcmwsIHN1cGFiYXNlQW5vbktleSwge1xuICBhdXRoOiB7XG4gICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSxcbiAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcbiAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUsXG4gIH0sXG59KVxuXG4vLyBFeHBvcnQgdHlwZXMgZm9yIGNvbnZlbmllbmNlXG5leHBvcnQgdHlwZSB7IERhdGFiYXNlIH0gZnJvbSAnQC9zaGFyZWQvdHlwZXMnXG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJFcnJvciIsInN1cGFiYXNlIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiIsImRldGVjdFNlc3Npb25JblVybCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BncmVlbm1pbGVzLWV2L3dlYi8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=ChevronDown,ChevronUp,Compare,ExternalLink,Trash2,X!=!../../node_modules/lucide-react/dist/esm/lucide-react.js":
/*!************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronDown,ChevronUp,Compare,ExternalLink,Trash2,X!=!../../node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronDown: () => (/* reexport safe */ _icons_chevron_down_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronUp: () => (/* reexport safe */ _icons_chevron_up_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ExternalLink: () => (/* reexport safe */ _icons_external_link_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_chevron_down_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/chevron-down.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _icons_chevron_up_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/chevron-up.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _icons_external_link_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/external-link.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/trash-2.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/x.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGV2cm9uRG93bixDaGV2cm9uVXAsQ29tcGFyZSxFeHRlcm5hbExpbmssVHJhc2gyLFghPSEuLi8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQ2dFO0FBQ0o7QUFDTTtBQUNaO0FBQ1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZ3JlZW5taWxlcy1ldi93ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/ZDZlYiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvbkRvd24gfSBmcm9tIFwiLi9pY29ucy9jaGV2cm9uLWRvd24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGV2cm9uVXAgfSBmcm9tIFwiLi9pY29ucy9jaGV2cm9uLXVwLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXh0ZXJuYWxMaW5rIH0gZnJvbSBcIi4vaWNvbnMvZXh0ZXJuYWwtbGluay5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRyYXNoMiB9IGZyb20gXCIuL2ljb25zL3RyYXNoLTIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYIH0gZnJvbSBcIi4vaWNvbnMveC5qc1wiIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJDaGV2cm9uRG93biIsIkNoZXZyb25VcCIsIkV4dGVybmFsTGluayIsIlRyYXNoMiIsIlgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=ChevronDown,ChevronUp,Compare,ExternalLink,Trash2,X!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/constants/ev-buyer-guide.ts":
/*!*************************************************************!*\
  !*** ../../packages/shared/src/constants/ev-buyer-guide.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BODY_TYPE_LABELS: () => (/* binding */ BODY_TYPE_LABELS),\n/* harmony export */   BUDGET_CATEGORIES: () => (/* binding */ BUDGET_CATEGORIES),\n/* harmony export */   CHARGING_CONNECTOR_TYPES: () => (/* binding */ CHARGING_CONNECTOR_TYPES),\n/* harmony export */   CHARGING_SPEED_CATEGORIES: () => (/* binding */ CHARGING_SPEED_CATEGORIES),\n/* harmony export */   COMPARISON_CATEGORIES: () => (/* binding */ COMPARISON_CATEGORIES),\n/* harmony export */   DAILY_DRIVING_CATEGORIES: () => (/* binding */ DAILY_DRIVING_CATEGORIES),\n/* harmony export */   DEFAULT_PAGE_SIZE: () => (/* binding */ DEFAULT_PAGE_SIZE),\n/* harmony export */   DEFAULT_PRIORITY_WEIGHTS: () => (/* binding */ DEFAULT_PRIORITY_WEIGHTS),\n/* harmony export */   FEATURE_CATEGORIES: () => (/* binding */ FEATURE_CATEGORIES),\n/* harmony export */   FILTER_PRESETS: () => (/* binding */ FILTER_PRESETS),\n/* harmony export */   MAX_COMPARISON_MODELS: () => (/* binding */ MAX_COMPARISON_MODELS),\n/* harmony export */   MAX_PAGE_SIZE: () => (/* binding */ MAX_PAGE_SIZE),\n/* harmony export */   MAX_SEARCH_SUGGESTIONS: () => (/* binding */ MAX_SEARCH_SUGGESTIONS),\n/* harmony export */   MIN_SEARCH_LENGTH: () => (/* binding */ MIN_SEARCH_LENGTH),\n/* harmony export */   PLACEHOLDER_IMAGES: () => (/* binding */ PLACEHOLDER_IMAGES),\n/* harmony export */   POPULAR_EV_MAKES: () => (/* binding */ POPULAR_EV_MAKES),\n/* harmony export */   PRICE_RANGES: () => (/* binding */ PRICE_RANGES),\n/* harmony export */   PRIORITY_FACTOR_LABELS: () => (/* binding */ PRIORITY_FACTOR_LABELS),\n/* harmony export */   PRODUCTION_STATUS_LABELS: () => (/* binding */ PRODUCTION_STATUS_LABELS),\n/* harmony export */   RANGE_CATEGORIES: () => (/* binding */ RANGE_CATEGORIES),\n/* harmony export */   SEARCH_DEBOUNCE_MS: () => (/* binding */ SEARCH_DEBOUNCE_MS),\n/* harmony export */   USE_CASE_LABELS: () => (/* binding */ USE_CASE_LABELS)\n/* harmony export */ });\n// ===== EV BUYER GUIDE CONSTANTS =====\n// Charging Connector Types\nconst CHARGING_CONNECTOR_TYPES = [\n    \"CCS\",\n    \"CHAdeMO\",\n    \"Tesla Supercharger\",\n    \"Type 2\",\n    \"J1772\",\n    \"GB/T\"\n];\n// Price Ranges for Filtering (in cents)\nconst PRICE_RANGES = [\n    {\n        label: \"Under $30k\",\n        min: 0,\n        max: 3000000\n    },\n    {\n        label: \"$30k - $50k\",\n        min: 3000000,\n        max: 5000000\n    },\n    {\n        label: \"$50k - $75k\",\n        min: 5000000,\n        max: 7500000\n    },\n    {\n        label: \"$75k - $100k\",\n        min: 7500000,\n        max: 10000000\n    },\n    {\n        label: \"Over $100k\",\n        min: 10000000,\n        max: 99999999\n    }\n];\n// Range Categories for Filtering\nconst RANGE_CATEGORIES = [\n    {\n        label: \"Under 200 miles\",\n        min: 0,\n        max: 199\n    },\n    {\n        label: \"200-300 miles\",\n        min: 200,\n        max: 300\n    },\n    {\n        label: \"300-400 miles\",\n        min: 300,\n        max: 400\n    },\n    {\n        label: \"Over 400 miles\",\n        min: 400,\n        max: 999\n    }\n];\n// Charging Speed Categories\nconst CHARGING_SPEED_CATEGORIES = [\n    {\n        label: \"Slow (Under 50kW)\",\n        min: 0,\n        max: 49\n    },\n    {\n        label: \"Fast (50-150kW)\",\n        min: 50,\n        max: 150\n    },\n    {\n        label: \"Ultra Fast (150kW+)\",\n        min: 150,\n        max: 999\n    }\n];\n// Body Type Display Names\nconst BODY_TYPE_LABELS = {\n    sedan: \"Sedan\",\n    suv: \"SUV\",\n    hatchback: \"Hatchback\",\n    truck: \"Pickup Truck\",\n    coupe: \"Coupe\",\n    wagon: \"Wagon\",\n    convertible: \"Convertible\",\n    crossover: \"Crossover\",\n    van: \"Van\"\n};\n// Production Status Display Names\nconst PRODUCTION_STATUS_LABELS = {\n    current: \"Currently Available\",\n    discontinued: \"Discontinued\",\n    concept: \"Concept\",\n    upcoming: \"Coming Soon\"\n};\n// Use Case Display Names\nconst USE_CASE_LABELS = {\n    commuting: \"Daily Commuting\",\n    family: \"Family Vehicle\",\n    performance: \"Performance Driving\",\n    luxury: \"Luxury Experience\",\n    utility: \"Work & Utility\",\n    eco_friendly: \"Environmental Focus\",\n    first_ev: \"First Electric Vehicle\",\n    upgrade: \"Upgrading from Gas\"\n};\n// Priority Factor Display Names\nconst PRIORITY_FACTOR_LABELS = {\n    price: \"Purchase Price\",\n    range: \"Driving Range\",\n    charging_speed: \"Charging Speed\",\n    performance: \"Performance\",\n    features: \"Technology & Features\",\n    safety: \"Safety Ratings\",\n    brand: \"Brand Reputation\",\n    efficiency: \"Energy Efficiency\",\n    size: \"Vehicle Size\",\n    cargo_space: \"Cargo Space\"\n};\n// Default Priority Weights for Different Use Cases\nconst DEFAULT_PRIORITY_WEIGHTS = {\n    commuting: {\n        price: 25,\n        range: 20,\n        charging_speed: 15,\n        efficiency: 15,\n        performance: 5,\n        features: 10,\n        safety: 5,\n        brand: 3,\n        size: 1,\n        cargo_space: 1\n    },\n    family: {\n        price: 20,\n        range: 15,\n        charging_speed: 10,\n        efficiency: 10,\n        performance: 5,\n        features: 10,\n        safety: 20,\n        brand: 5,\n        size: 3,\n        cargo_space: 2\n    },\n    performance: {\n        price: 10,\n        range: 10,\n        charging_speed: 15,\n        efficiency: 5,\n        performance: 30,\n        features: 15,\n        safety: 10,\n        brand: 3,\n        size: 1,\n        cargo_space: 1\n    },\n    luxury: {\n        price: 5,\n        range: 15,\n        charging_speed: 10,\n        efficiency: 5,\n        performance: 15,\n        features: 25,\n        safety: 15,\n        brand: 8,\n        size: 1,\n        cargo_space: 1\n    },\n    utility: {\n        price: 20,\n        range: 15,\n        charging_speed: 10,\n        efficiency: 15,\n        performance: 10,\n        features: 5,\n        safety: 10,\n        brand: 3,\n        size: 7,\n        cargo_space: 5\n    },\n    eco_friendly: {\n        price: 15,\n        range: 20,\n        charging_speed: 10,\n        efficiency: 25,\n        performance: 5,\n        features: 10,\n        safety: 10,\n        brand: 3,\n        size: 1,\n        cargo_space: 1\n    },\n    first_ev: {\n        price: 25,\n        range: 20,\n        charging_speed: 15,\n        efficiency: 10,\n        performance: 5,\n        features: 10,\n        safety: 10,\n        brand: 3,\n        size: 1,\n        cargo_space: 1\n    },\n    upgrade: {\n        price: 20,\n        range: 18,\n        charging_speed: 12,\n        efficiency: 12,\n        performance: 8,\n        features: 12,\n        safety: 12,\n        brand: 4,\n        size: 1,\n        cargo_space: 1\n    }\n};\n// Filter Presets for Quick Selection\nconst FILTER_PRESETS = [\n    {\n        name: \"Budget Friendly\",\n        description: \"Affordable EVs under $50k\",\n        filters: {\n            priceMax: 5000000,\n            productionStatus: \"current\"\n        }\n    },\n    {\n        name: \"Long Range\",\n        description: \"EVs with 300+ miles range\",\n        filters: {\n            rangeMin: 300,\n            productionStatus: \"current\"\n        }\n    },\n    {\n        name: \"Fast Charging\",\n        description: \"EVs with 150kW+ charging\",\n        filters: {\n            chargingSpeedMin: 150,\n            productionStatus: \"current\"\n        }\n    },\n    {\n        name: \"Family SUVs\",\n        description: \"Family-friendly SUVs and crossovers\",\n        filters: {\n            bodyTypes: [\n                \"suv\",\n                \"crossover\"\n            ],\n            seatingCapacity: 5,\n            productionStatus: \"current\"\n        }\n    },\n    {\n        name: \"Performance\",\n        description: \"High-performance EVs\",\n        filters: {\n            accelerationMax: 4.0,\n            productionStatus: \"current\"\n        }\n    },\n    {\n        name: \"Best Value\",\n        description: \"Editor's choice for best value\",\n        filters: {\n            bestValue: true,\n            productionStatus: \"current\"\n        }\n    }\n];\n// Comparison Categories for Side-by-Side View\nconst COMPARISON_CATEGORIES = [\n    {\n        name: \"Pricing\",\n        fields: [\n            \"price_msrp\",\n            \"price_base\",\n            \"total_cost_ownership\"\n        ]\n    },\n    {\n        name: \"Range & Efficiency\",\n        fields: [\n            \"range_epa_miles\",\n            \"range_real_world_miles\",\n            \"efficiency_mpge\",\n            \"battery_capacity_kwh\"\n        ]\n    },\n    {\n        name: \"Charging\",\n        fields: [\n            \"charging_speed_dc_kw\",\n            \"charging_speed_ac_kw\",\n            \"charging_time_10_80_minutes\",\n            \"charging_ports\"\n        ]\n    },\n    {\n        name: \"Performance\",\n        fields: [\n            \"acceleration_0_60_mph\",\n            \"top_speed_mph\",\n            \"motor_power_hp\",\n            \"drivetrain\"\n        ]\n    },\n    {\n        name: \"Dimensions\",\n        fields: [\n            \"length_inches\",\n            \"width_inches\",\n            \"height_inches\",\n            \"cargo_volume_cubic_ft\",\n            \"seating_capacity\"\n        ]\n    },\n    {\n        name: \"Safety & Warranty\",\n        fields: [\n            \"safety_ratings\",\n            \"warranty_info\"\n        ]\n    }\n];\n// Popular EV Makes (for autocomplete and suggestions)\nconst POPULAR_EV_MAKES = [\n    \"Tesla\",\n    \"BMW\",\n    \"Mercedes-Benz\",\n    \"Audi\",\n    \"Nissan\",\n    \"Ford\",\n    \"Chevrolet\",\n    \"Volkswagen\",\n    \"Hyundai\",\n    \"Kia\",\n    \"Porsche\",\n    \"Volvo\",\n    \"Polestar\",\n    \"Lucid\",\n    \"Rivian\"\n];\n// Recommended Daily Driving Miles Categories\nconst DAILY_DRIVING_CATEGORIES = [\n    {\n        label: \"Under 25 miles\",\n        value: 25\n    },\n    {\n        label: \"25-50 miles\",\n        value: 50\n    },\n    {\n        label: \"50-100 miles\",\n        value: 100\n    },\n    {\n        label: \"100-150 miles\",\n        value: 150\n    },\n    {\n        label: \"Over 150 miles\",\n        value: 200\n    }\n];\n// Budget Categories for Preferences\nconst BUDGET_CATEGORIES = [\n    {\n        label: \"Under $30,000\",\n        min: 0,\n        max: 3000000\n    },\n    {\n        label: \"$30,000 - $50,000\",\n        min: 3000000,\n        max: 5000000\n    },\n    {\n        label: \"$50,000 - $75,000\",\n        min: 5000000,\n        max: 7500000\n    },\n    {\n        label: \"$75,000 - $100,000\",\n        min: 7500000,\n        max: 10000000\n    },\n    {\n        label: \"$100,000 - $150,000\",\n        min: 10000000,\n        max: 15000000\n    },\n    {\n        label: \"Over $150,000\",\n        min: 15000000,\n        max: 99999999\n    }\n];\n// API Pagination Defaults\nconst DEFAULT_PAGE_SIZE = 20;\nconst MAX_PAGE_SIZE = 100;\nconst MAX_COMPARISON_MODELS = 4;\n// Search Configuration\nconst MIN_SEARCH_LENGTH = 2;\nconst SEARCH_DEBOUNCE_MS = 300;\nconst MAX_SEARCH_SUGGESTIONS = 5;\n// Image Placeholder URLs\nconst PLACEHOLDER_IMAGES = {\n    ev_model: \"https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=800&h=600&fit=crop&crop=center\",\n    manufacturer_logo: \"https://via.placeholder.com/100x100/6366f1/ffffff?text=Logo\",\n    no_image: \"https://via.placeholder.com/400x300/e5e7eb/9ca3af?text=No+Image\"\n};\n// Feature Categories for EV Models\nconst FEATURE_CATEGORIES = [\n    \"Autopilot/Self-Driving\",\n    \"Premium Audio\",\n    \"Panoramic Roof\",\n    \"Heated Seats\",\n    \"Ventilated Seats\",\n    \"Wireless Charging\",\n    \"Premium Interior\",\n    \"Air Suspension\",\n    \"Towing Package\",\n    \"Off-Road Package\",\n    \"Performance Package\",\n    \"Cold Weather Package\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/constants/ev-buyer-guide.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/utils/ev-buyer-guide.ts":
/*!*********************************************************!*\
  !*** ../../packages/shared/src/utils/ev-buyer-guide.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateBuyerMetrics: () => (/* binding */ calculateBuyerMetrics),\n/* harmony export */   calculateComparisonMetrics: () => (/* binding */ calculateComparisonMetrics),\n/* harmony export */   calculateMatchScore: () => (/* binding */ calculateMatchScore),\n/* harmony export */   compareValues: () => (/* binding */ compareValues),\n/* harmony export */   formatAcceleration: () => (/* binding */ formatAcceleration),\n/* harmony export */   formatBatteryCapacity: () => (/* binding */ formatBatteryCapacity),\n/* harmony export */   formatChargingSpeed: () => (/* binding */ formatChargingSpeed),\n/* harmony export */   formatChargingTime: () => (/* binding */ formatChargingTime),\n/* harmony export */   formatEfficiency: () => (/* binding */ formatEfficiency),\n/* harmony export */   formatPower: () => (/* binding */ formatPower),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatRange: () => (/* binding */ formatRange),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getBestInComparison: () => (/* binding */ getBestInComparison),\n/* harmony export */   matchesSearchCriteria: () => (/* binding */ matchesSearchCriteria),\n/* harmony export */   parseSlug: () => (/* binding */ parseSlug),\n/* harmony export */   validateEVModelData: () => (/* binding */ validateEVModelData)\n/* harmony export */ });\n// ===== FORMATTING UTILITIES =====\n/**\n * Format price in cents to display format\n */ function formatPrice(priceInCents) {\n    if (!priceInCents || priceInCents <= 0) return \"Price not available\";\n    const price = priceInCents / 100;\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency: \"USD\",\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(price);\n}\n/**\n * Format range with units\n */ function formatRange(miles) {\n    if (!miles || miles <= 0) return \"Range not available\";\n    return `${miles} miles`;\n}\n/**\n * Format efficiency (MPGe)\n */ function formatEfficiency(mpge) {\n    if (!mpge || mpge <= 0) return \"Efficiency not available\";\n    return `${mpge} MPGe`;\n}\n/**\n * Format acceleration time\n */ function formatAcceleration(seconds) {\n    if (!seconds || seconds <= 0) return \"Acceleration not available\";\n    return `${seconds}s (0-60 mph)`;\n}\n/**\n * Format charging time\n */ function formatChargingTime(minutes) {\n    if (!minutes || minutes <= 0) return \"Charging time not available\";\n    if (minutes >= 60) {\n        const hours = Math.floor(minutes / 60);\n        const remainingMinutes = minutes % 60;\n        return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m (10-80%)` : `${hours}h (10-80%)`;\n    }\n    return `${minutes}m (10-80%)`;\n}\n/**\n * Format power with units\n */ function formatPower(hp) {\n    if (!hp || hp <= 0) return \"Power not available\";\n    return `${hp} hp`;\n}\n/**\n * Format battery capacity\n */ function formatBatteryCapacity(kwh) {\n    if (!kwh || kwh <= 0) return \"Battery capacity not available\";\n    return `${kwh} kWh`;\n}\n/**\n * Format charging speed\n */ function formatChargingSpeed(kw) {\n    if (!kw || kw <= 0) return \"Charging speed not available\";\n    return `${kw} kW`;\n}\n// ===== CALCULATION UTILITIES =====\n/**\n * Calculate buyer-focused metrics for an EV model\n */ function calculateBuyerMetrics(evModel) {\n    const costPerMile = evModel.price_msrp && evModel.range_epa_miles && evModel.range_epa_miles > 0 ? Math.round(evModel.price_msrp / 100 / evModel.range_epa_miles) : null;\n    const chargingEfficiency = evModel.charging_speed_dc_kw && evModel.battery_capacity_kwh && evModel.battery_capacity_kwh > 0 ? Math.round(evModel.charging_speed_dc_kw / evModel.battery_capacity_kwh * 100) / 100 : null;\n    const practicalRange = evModel.range_real_world_miles || (evModel.range_epa_miles ? Math.round(evModel.range_epa_miles * 0.85) : null);\n    const powerToWeightRatio = evModel.motor_power_hp && evModel.curb_weight_lbs && evModel.curb_weight_lbs > 0 ? Math.round(evModel.motor_power_hp / evModel.curb_weight_lbs * 1000) / 1000 : null;\n    return {\n        costPerMile,\n        chargingEfficiency,\n        practicalRange,\n        powerToWeightRatio\n    };\n}\n/**\n * Calculate comparison metrics for multiple EV models\n */ function calculateComparisonMetrics(evModels) {\n    const prices = evModels.map((m)=>m.price_msrp || 0).filter((p)=>p > 0);\n    const ranges = evModels.map((m)=>m.range_epa_miles || 0).filter((r)=>r > 0);\n    const chargingSpeeds = evModels.map((m)=>m.charging_speed_dc_kw || 0).filter((s)=>s > 0);\n    const accelerations = evModels.map((m)=>m.acceleration_0_60_mph || 999).filter((a)=>a < 999);\n    return {\n        priceRange: {\n            min: prices.length > 0 ? Math.min(...prices) : 0,\n            max: prices.length > 0 ? Math.max(...prices) : 0\n        },\n        rangeComparison: {\n            min: ranges.length > 0 ? Math.min(...ranges) : 0,\n            max: ranges.length > 0 ? Math.max(...ranges) : 0\n        },\n        chargingSpeed: {\n            min: chargingSpeeds.length > 0 ? Math.min(...chargingSpeeds) : 0,\n            max: chargingSpeeds.length > 0 ? Math.max(...chargingSpeeds) : 0\n        },\n        acceleration: {\n            fastest: accelerations.length > 0 ? Math.min(...accelerations) : 0,\n            slowest: accelerations.length > 0 ? Math.max(...accelerations) : 0\n        }\n    };\n}\n/**\n * Calculate match score for an EV model based on user preferences\n */ function calculateMatchScore(evModel, preferences) {\n    let score = 0;\n    // Budget match (30 points max)\n    if (evModel.price_msrp) {\n        const budgetMin = preferences.budget_min || 0;\n        const budgetMax = preferences.budget_max || 999999999;\n        if (evModel.price_msrp >= budgetMin && evModel.price_msrp <= budgetMax) {\n            score += 30;\n        }\n    }\n    // Range requirement (25 points max)\n    if (evModel.range_epa_miles && preferences.range_requirement_miles) {\n        if (evModel.range_epa_miles >= preferences.range_requirement_miles) {\n            score += 25;\n        }\n    }\n    // Body type preference (20 points max)\n    if (evModel.body_type && preferences.body_type_preferences) {\n        if (preferences.body_type_preferences.includes(evModel.body_type)) {\n            score += 20;\n        }\n    }\n    // Featured model bonus (10 points)\n    if (evModel.is_featured) {\n        score += 10;\n    }\n    // Best value bonus (10 points)\n    if (evModel.best_value) {\n        score += 10;\n    }\n    // Popularity score (up to 10 points)\n    score += Math.min(evModel.popularity_score / 10, 10);\n    return Math.round(score);\n}\n/**\n * Generate URL-friendly slug from EV model\n */ function generateSlug(evModel) {\n    const parts = [\n        evModel.make,\n        evModel.model,\n        evModel.year.toString(),\n        evModel.trim\n    ].filter(Boolean);\n    return parts.join(\"-\").toLowerCase().replace(/[^a-z0-9-]/g, \"-\").replace(/-+/g, \"-\").replace(/^-|-$/g, \"\");\n}\n/**\n * Parse slug back to search parameters\n */ function parseSlug(slug) {\n    const parts = slug.split(\"-\");\n    if (parts.length < 3) return {};\n    const year = parseInt(parts[parts.length - 2]);\n    if (isNaN(year)) return {};\n    return {\n        make: parts[0],\n        model: parts[1],\n        year,\n        trim: parts.length > 3 ? parts.slice(2, -1).join(\"-\") : undefined\n    };\n}\n// ===== COMPARISON UTILITIES =====\n/**\n * Compare two values and return comparison result\n */ function compareValues(value1, value2, higherIsBetter = true) {\n    if (value1 == null || value2 == null) return \"unknown\";\n    if (value1 === value2) return \"equal\";\n    if (higherIsBetter) {\n        return value1 > value2 ? \"better\" : \"worse\";\n    } else {\n        return value1 < value2 ? \"better\" : \"worse\";\n    }\n}\n/**\n * Get the best value in a comparison for a specific metric\n */ function getBestInComparison(evModels, metric, higherIsBetter = true) {\n    const modelsWithValue = evModels.map((model)=>({\n            model,\n            value: model[metric]\n        })).filter((item)=>item.value != null && item.value !== 0);\n    if (modelsWithValue.length === 0) return null;\n    const best = modelsWithValue.reduce((best, current)=>{\n        if (higherIsBetter) {\n            return (current.value || 0) > (best.value || 0) ? current : best;\n        } else {\n            return (current.value || 0) < (best.value || 0) ? current : best;\n        }\n    });\n    return best;\n}\n// ===== VALIDATION UTILITIES =====\n/**\n * Validate EV model data completeness\n */ function validateEVModelData(evModel) {\n    const requiredFields = [\n        \"make\",\n        \"model\",\n        \"year\",\n        \"battery_capacity_kwh\"\n    ];\n    const importantFields = [\n        \"price_msrp\",\n        \"range_epa_miles\",\n        \"charging_speed_dc_kw\"\n    ];\n    const missingFields = requiredFields.filter((field)=>!evModel[field]);\n    const missingImportant = importantFields.filter((field)=>!evModel[field]);\n    return {\n        isValid: missingFields.length === 0,\n        missingFields,\n        warnings: missingImportant.map((field)=>`Missing important field: ${field}`)\n    };\n}\n/**\n * Check if EV model matches search criteria\n */ function matchesSearchCriteria(evModel, criteria) {\n    // Text search\n    if (criteria.query) {\n        const searchText = `${evModel.make} ${evModel.model} ${evModel.trim || \"\"}`.toLowerCase();\n        if (!searchText.includes(criteria.query.toLowerCase())) {\n            return false;\n        }\n    }\n    // Make filter\n    if (criteria.make && evModel.make.toLowerCase() !== criteria.make.toLowerCase()) {\n        return false;\n    }\n    // Body type filter\n    if (criteria.bodyType && evModel.body_type !== criteria.bodyType) {\n        return false;\n    }\n    // Price range filter\n    if (criteria.priceRange && evModel.price_msrp) {\n        if (evModel.price_msrp < criteria.priceRange.min || evModel.price_msrp > criteria.priceRange.max) {\n            return false;\n        }\n    }\n    // Range filter\n    if (criteria.rangeMin && evModel.range_epa_miles) {\n        if (evModel.range_epa_miles < criteria.rangeMin) {\n            return false;\n        }\n    }\n    // Production status filter\n    if (criteria.productionStatus && evModel.production_status !== criteria.productionStatus) {\n        return false;\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/utils/ev-buyer-guide.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"067048cfb679\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGdyZWVubWlsZXMtZXYvd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz81YmI5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDY3MDQ4Y2ZiNjc5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_AuthErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthErrorBoundary */ \"(rsc)/./src/components/AuthErrorBoundary.tsx\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_ToastProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ToastProvider */ \"(rsc)/./src/components/ToastProvider.tsx\");\n/* harmony import */ var _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/ComparisonContext */ \"(rsc)/./src/contexts/ComparisonContext.tsx\");\n/* harmony import */ var _components_comparison_ComparisonPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/comparison/ComparisonPanel */ \"(rsc)/./src/components/comparison/ComparisonPanel.tsx\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"GreenMilesEV - Electric Vehicle Management\",\n    description: \"Comprehensive platform for electric vehicle management, charging, and trip planning\",\n    keywords: [\n        \"electric vehicle\",\n        \"EV\",\n        \"charging\",\n        \"green energy\",\n        \"sustainability\"\n    ],\n    authors: [\n        {\n            name: \"GreenMilesEV Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.AuthErrorBoundary, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_6__.ComparisonProvider, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-h-screen bg-background\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comparison_ComparisonPanel__WEBPACK_IMPORTED_MODULE_7__.ComparisonPanel, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToastProvider__WEBPACK_IMPORTED_MODULE_5__.ToastProvider, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AuthErrorBoundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/AuthErrorBoundary.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthErrorBoundary: () => (/* binding */ e0),
/* harmony export */   useAuthErrorHandler: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx#AuthErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx#useAuthErrorHandler`);


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ThemeProvider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./src/components/ToastProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ToastProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ToastProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\ToastProvider.tsx#ToastProvider`);


/***/ }),

/***/ "(rsc)/./src/components/comparison/ComparisonPanel.tsx":
/*!*******************************************************!*\
  !*** ./src/components/comparison/ComparisonPanel.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ComparisonPanel: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\comparison\ComparisonPanel.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\comparison\ComparisonPanel.tsx#ComparisonPanel`);


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#withAuth`);


/***/ }),

/***/ "(rsc)/./src/contexts/ComparisonContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/ComparisonContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ComparisonProvider: () => (/* binding */ e0),
/* harmony export */   MAX_COMPARISON_MODELS: () => (/* binding */ e2),
/* harmony export */   useComparison: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\ComparisonContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\ComparisonContext.tsx#ComparisonProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\ComparisonContext.tsx#useComparison`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\ComparisonContext.tsx#MAX_COMPARISON_MODELS`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/sonner","vendor-chunks/lucide-react","vendor-chunks/react-hot-toast","vendor-chunks/class-variance-authority","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2F..%2F..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();