{"name": "@greenmiles-ev/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.0.2", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.38.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "next": "14.0.4", "next-themes": "^0.4.6", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.0", "typescript": "^5"}}