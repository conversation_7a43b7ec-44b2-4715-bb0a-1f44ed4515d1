import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/shared/types'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100) // Max 100 items per page
    const offset = (page - 1) * limit
    
    // Filter parameters
    const make = searchParams.get('make')
    const bodyType = searchParams.get('body_type')
    const priceMin = searchParams.get('price_min')
    const priceMax = searchParams.get('price_max')
    const rangeMin = searchParams.get('range_min')
    const productionStatus = searchParams.get('production_status') || 'current'
    const featured = searchParams.get('featured')
    const bestValue = searchParams.get('best_value')
    
    // Sort parameters
    const sortBy = searchParams.get('sort_by') || 'popularity_score'
    const sortOrder = searchParams.get('sort_order') || 'desc'
    
    // Search parameter
    const search = searchParams.get('search')

    // Build query
    let query = supabase
      .from('ev_models')
      .select(`
        id,
        make,
        model,
        year,
        trim,
        body_type,
        price_msrp,
        price_base,
        production_status,
        battery_capacity_kwh,
        range_epa_miles,
        range_real_world_miles,
        efficiency_mpge,
        charging_speed_dc_kw,
        charging_time_10_80_minutes,
        acceleration_0_60_mph,
        seating_capacity,
        images,
        is_featured,
        popularity_score,
        editor_choice,
        best_value,
        ev_manufacturers!inner(
          name,
          logo_url
        )
      `)

    // Apply filters
    if (make) {
      query = query.eq('make', make)
    }
    
    if (bodyType) {
      query = query.eq('body_type', bodyType)
    }
    
    if (priceMin) {
      query = query.gte('price_msrp', parseInt(priceMin))
    }
    
    if (priceMax) {
      query = query.lte('price_msrp', parseInt(priceMax))
    }
    
    if (rangeMin) {
      query = query.gte('range_epa_miles', parseInt(rangeMin))
    }
    
    if (productionStatus) {
      query = query.eq('production_status', productionStatus)
    }
    
    if (featured === 'true') {
      query = query.eq('is_featured', true)
    }
    
    if (bestValue === 'true') {
      query = query.eq('best_value', true)
    }

    // Apply search
    if (search) {
      query = query.or(`make.ilike.%${search}%,model.ilike.%${search}%,trim.ilike.%${search}%`)
    }

    // Apply sorting
    const validSortFields = ['popularity_score', 'price_msrp', 'range_epa_miles', 'year', 'make', 'model']
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'popularity_score'
    const order = sortOrder === 'asc' ? 'asc' : 'desc'
    
    query = query.order(sortField, { ascending: order === 'asc' })
    
    // Add secondary sort for consistency
    if (sortField !== 'popularity_score') {
      query = query.order('popularity_score', { ascending: false })
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    const { data, error, count } = await query

    if (error) {
      console.error('Error fetching EV models:', error)
      return NextResponse.json(
        { error: 'Failed to fetch EV models' },
        { status: 500 }
      )
    }

    // Get total count for pagination
    const { count: totalCount } = await supabase
      .from('ev_models')
      .select('*', { count: 'exact', head: true })

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit),
        hasNext: offset + limit < (totalCount || 0),
        hasPrev: page > 1
      },
      filters: {
        make,
        bodyType,
        priceMin,
        priceMax,
        rangeMin,
        productionStatus,
        featured,
        bestValue,
        search
      },
      sort: {
        sortBy: sortField,
        sortOrder: order
      }
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
