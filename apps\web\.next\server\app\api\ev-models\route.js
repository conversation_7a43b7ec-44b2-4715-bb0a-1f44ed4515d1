/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ev-models/route";
exports.ids = ["app/api/ev-models/route"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!****************************************************************!*\
  !*** ../../node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \****************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fev-models%2Froute&page=%2Fapi%2Fev-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fev-models%2Froute.ts&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fev-models%2Froute&page=%2Fapi%2Fev-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fev-models%2Froute.ts&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_obi_c_Desktop_ev_app_apps_web_src_app_api_ev_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ev-models/route.ts */ \"(rsc)/./src/app/api/ev-models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ev-models/route\",\n        pathname: \"/api/ev-models\",\n        filename: \"route\",\n        bundlePath: \"app/api/ev-models/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\api\\\\ev-models\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_obi_c_Desktop_ev_app_apps_web_src_app_api_ev_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/ev-models/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fev-models%2Froute&page=%2Fapi%2Fev-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fev-models%2Froute.ts&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/ev-models/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/ev-models/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/../../node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://pbevpexclffmhqstwlha.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZXZwZXhjbGZmbWhxc3R3bGhhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjA1MzksImV4cCI6MjA2NTQ5NjUzOX0.F6bMKoeKV5QTyt0oEfNDn6-s780wkvvddplEBUuRpsI\");\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Parse query parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = Math.min(parseInt(searchParams.get(\"limit\") || \"20\"), 100) // Max 100 items per page\n        ;\n        const offset = (page - 1) * limit;\n        // Filter parameters\n        const make = searchParams.get(\"make\");\n        const bodyType = searchParams.get(\"body_type\");\n        const priceMin = searchParams.get(\"price_min\");\n        const priceMax = searchParams.get(\"price_max\");\n        const rangeMin = searchParams.get(\"range_min\");\n        const productionStatus = searchParams.get(\"production_status\") || \"current\";\n        const featured = searchParams.get(\"featured\");\n        const bestValue = searchParams.get(\"best_value\");\n        // Sort parameters\n        const sortBy = searchParams.get(\"sort_by\") || \"popularity_score\";\n        const sortOrder = searchParams.get(\"sort_order\") || \"desc\";\n        // Search parameter\n        const search = searchParams.get(\"search\");\n        // Build query with manufacturer join\n        let query = supabase.from(\"ev_models\").select(`\n        id,\n        make,\n        model,\n        year,\n        trim,\n        body_type,\n        price_msrp,\n        price_base,\n        production_status,\n        battery_capacity_kwh,\n        range_epa_miles,\n        range_real_world_miles,\n        efficiency_mpge,\n        charging_speed_dc_kw,\n        charging_time_10_80_minutes,\n        acceleration_0_60_mph,\n        seating_capacity,\n        images,\n        is_featured,\n        popularity_score,\n        editor_choice,\n        best_value,\n        ev_manufacturers!inner(\n          name,\n          logo_url\n        )\n      `);\n        // Apply filters\n        if (make) {\n            query = query.eq(\"make\", make);\n        }\n        if (bodyType) {\n            query = query.eq(\"body_type\", bodyType);\n        }\n        if (priceMin) {\n            query = query.gte(\"price_msrp\", parseInt(priceMin));\n        }\n        if (priceMax) {\n            query = query.lte(\"price_msrp\", parseInt(priceMax));\n        }\n        if (rangeMin) {\n            query = query.gte(\"range_epa_miles\", parseInt(rangeMin));\n        }\n        if (productionStatus) {\n            query = query.eq(\"production_status\", productionStatus);\n        }\n        if (featured === \"true\") {\n            query = query.eq(\"is_featured\", true);\n        }\n        if (bestValue === \"true\") {\n            query = query.eq(\"best_value\", true);\n        }\n        // Apply search\n        if (search) {\n            query = query.or(`make.ilike.%${search}%,model.ilike.%${search}%,trim.ilike.%${search}%`);\n        }\n        // Apply sorting\n        const validSortFields = [\n            \"popularity_score\",\n            \"price_msrp\",\n            \"range_epa_miles\",\n            \"year\",\n            \"make\",\n            \"model\"\n        ];\n        const sortField = validSortFields.includes(sortBy) ? sortBy : \"popularity_score\";\n        const order = sortOrder === \"asc\" ? \"asc\" : \"desc\";\n        query = query.order(sortField, {\n            ascending: order === \"asc\"\n        });\n        // Add secondary sort for consistency\n        if (sortField !== \"popularity_score\") {\n            query = query.order(\"popularity_score\", {\n                ascending: false\n            });\n        }\n        // Apply pagination\n        query = query.range(offset, offset + limit - 1);\n        const { data, error, count } = await query;\n        if (error) {\n            console.error(\"Error fetching EV models:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to fetch EV models\"\n            }, {\n                status: 500\n            });\n        }\n        // Get total count for pagination\n        const { count: totalCount } = await supabase.from(\"ev_models\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data,\n            pagination: {\n                page,\n                limit,\n                total: totalCount || 0,\n                totalPages: Math.ceil((totalCount || 0) / limit),\n                hasNext: offset + limit < (totalCount || 0),\n                hasPrev: page > 1\n            },\n            filters: {\n                make,\n                bodyType,\n                priceMin,\n                priceMax,\n                rangeMin,\n                productionStatus,\n                featured,\n                bestValue,\n                search\n            },\n            sort: {\n                sortBy: sortField,\n                sortOrder: order\n            }\n        });\n    } catch (error) {\n        console.error(\"Unexpected error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9ldi1tb2RlbHMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVEO0FBQ0g7QUFHcEQsTUFBTUUsV0FBV0QsbUVBQVlBLENBQzNCRSwwQ0FBb0MsRUFDcENBLGtOQUF5QztBQUdwQyxlQUFlSSxJQUFJQyxPQUFvQjtJQUM1QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJRixRQUFRRyxHQUFHO1FBRTVDLHlCQUF5QjtRQUN6QixNQUFNQyxPQUFPQyxTQUFTSixhQUFhSyxHQUFHLENBQUMsV0FBVztRQUNsRCxNQUFNQyxRQUFRQyxLQUFLQyxHQUFHLENBQUNKLFNBQVNKLGFBQWFLLEdBQUcsQ0FBQyxZQUFZLE9BQU8sS0FBSyx5QkFBeUI7O1FBQ2xHLE1BQU1JLFNBQVMsQ0FBQ04sT0FBTyxLQUFLRztRQUU1QixvQkFBb0I7UUFDcEIsTUFBTUksT0FBT1YsYUFBYUssR0FBRyxDQUFDO1FBQzlCLE1BQU1NLFdBQVdYLGFBQWFLLEdBQUcsQ0FBQztRQUNsQyxNQUFNTyxXQUFXWixhQUFhSyxHQUFHLENBQUM7UUFDbEMsTUFBTVEsV0FBV2IsYUFBYUssR0FBRyxDQUFDO1FBQ2xDLE1BQU1TLFdBQVdkLGFBQWFLLEdBQUcsQ0FBQztRQUNsQyxNQUFNVSxtQkFBbUJmLGFBQWFLLEdBQUcsQ0FBQyx3QkFBd0I7UUFDbEUsTUFBTVcsV0FBV2hCLGFBQWFLLEdBQUcsQ0FBQztRQUNsQyxNQUFNWSxZQUFZakIsYUFBYUssR0FBRyxDQUFDO1FBRW5DLGtCQUFrQjtRQUNsQixNQUFNYSxTQUFTbEIsYUFBYUssR0FBRyxDQUFDLGNBQWM7UUFDOUMsTUFBTWMsWUFBWW5CLGFBQWFLLEdBQUcsQ0FBQyxpQkFBaUI7UUFFcEQsbUJBQW1CO1FBQ25CLE1BQU1lLFNBQVNwQixhQUFhSyxHQUFHLENBQUM7UUFFaEMscUNBQXFDO1FBQ3JDLElBQUlnQixRQUFRNUIsU0FBUzZCLElBQUksQ0FBQyxhQUFhQyxNQUFNLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O01BMkI3QyxDQUFDO1FBRUgsZ0JBQWdCO1FBQ2hCLElBQUliLE1BQU07WUFDUlcsUUFBUUEsTUFBTUcsRUFBRSxDQUFDLFFBQVFkO1FBQzNCO1FBRUEsSUFBSUMsVUFBVTtZQUNaVSxRQUFRQSxNQUFNRyxFQUFFLENBQUMsYUFBYWI7UUFDaEM7UUFFQSxJQUFJQyxVQUFVO1lBQ1pTLFFBQVFBLE1BQU1JLEdBQUcsQ0FBQyxjQUFjckIsU0FBU1E7UUFDM0M7UUFFQSxJQUFJQyxVQUFVO1lBQ1pRLFFBQVFBLE1BQU1LLEdBQUcsQ0FBQyxjQUFjdEIsU0FBU1M7UUFDM0M7UUFFQSxJQUFJQyxVQUFVO1lBQ1pPLFFBQVFBLE1BQU1JLEdBQUcsQ0FBQyxtQkFBbUJyQixTQUFTVTtRQUNoRDtRQUVBLElBQUlDLGtCQUFrQjtZQUNwQk0sUUFBUUEsTUFBTUcsRUFBRSxDQUFDLHFCQUFxQlQ7UUFDeEM7UUFFQSxJQUFJQyxhQUFhLFFBQVE7WUFDdkJLLFFBQVFBLE1BQU1HLEVBQUUsQ0FBQyxlQUFlO1FBQ2xDO1FBRUEsSUFBSVAsY0FBYyxRQUFRO1lBQ3hCSSxRQUFRQSxNQUFNRyxFQUFFLENBQUMsY0FBYztRQUNqQztRQUVBLGVBQWU7UUFDZixJQUFJSixRQUFRO1lBQ1ZDLFFBQVFBLE1BQU1NLEVBQUUsQ0FBQyxDQUFDLFlBQVksRUFBRVAsT0FBTyxlQUFlLEVBQUVBLE9BQU8sY0FBYyxFQUFFQSxPQUFPLENBQUMsQ0FBQztRQUMxRjtRQUVBLGdCQUFnQjtRQUNoQixNQUFNUSxrQkFBa0I7WUFDdEI7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDRCxNQUFNQyxZQUFZRCxnQkFBZ0JFLFFBQVEsQ0FBQ1osVUFBVUEsU0FBUztRQUM5RCxNQUFNYSxRQUFRWixjQUFjLFFBQVEsUUFBUTtRQUU1Q0UsUUFBUUEsTUFBTVUsS0FBSyxDQUFDRixXQUFXO1lBQUVHLFdBQVdELFVBQVU7UUFBTTtRQUU1RCxxQ0FBcUM7UUFDckMsSUFBSUYsY0FBYyxvQkFBb0I7WUFDcENSLFFBQVFBLE1BQU1VLEtBQUssQ0FBQyxvQkFBb0I7Z0JBQUVDLFdBQVc7WUFBTTtRQUM3RDtRQUVBLG1CQUFtQjtRQUNuQlgsUUFBUUEsTUFBTVksS0FBSyxDQUFDeEIsUUFBUUEsU0FBU0gsUUFBUTtRQUU3QyxNQUFNLEVBQUU0QixJQUFJLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTWY7UUFFckMsSUFBSWMsT0FBTztZQUNURSxRQUFRRixLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxPQUFPNUMsa0ZBQVlBLENBQUMrQyxJQUFJLENBQUM7Z0JBQUVILE9BQU87WUFBNEIsR0FBRztnQkFBRUksUUFBUTtZQUFJO1FBQ2pGO1FBRUEsaUNBQWlDO1FBQ2pDLE1BQU0sRUFBRUgsT0FBT0ksVUFBVSxFQUFFLEdBQUcsTUFBTS9DLFNBQ2pDNkIsSUFBSSxDQUFDLGFBQ0xDLE1BQU0sQ0FBQyxLQUFLO1lBQUVhLE9BQU87WUFBU0ssTUFBTTtRQUFLO1FBRTVDLE9BQU9sRCxrRkFBWUEsQ0FBQytDLElBQUksQ0FBQztZQUN2Qko7WUFDQVEsWUFBWTtnQkFDVnZDO2dCQUNBRztnQkFDQXFDLE9BQU9ILGNBQWM7Z0JBQ3JCSSxZQUFZckMsS0FBS3NDLElBQUksQ0FBQyxDQUFDTCxjQUFjLEtBQUtsQztnQkFDMUN3QyxTQUFTckMsU0FBU0gsUUFBU2tDLENBQUFBLGNBQWM7Z0JBQ3pDTyxTQUFTNUMsT0FBTztZQUNsQjtZQUNBNkMsU0FBUztnQkFDUHRDO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO2dCQUNBQztnQkFDQUc7WUFDRjtZQUNBNkIsTUFBTTtnQkFDSi9CLFFBQVFXO2dCQUNSVixXQUFXWTtZQUNiO1FBQ0Y7SUFDRixFQUFFLE9BQU9JLE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLHFCQUFxQkE7UUFDbkMsT0FBTzVDLGtGQUFZQSxDQUFDK0MsSUFBSSxDQUFDO1lBQUVILE9BQU87UUFBd0IsR0FBRztZQUFFSSxRQUFRO1FBQUk7SUFDN0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL0BncmVlbm1pbGVzLWV2L3dlYi8uL3NyYy9hcHAvYXBpL2V2LW1vZGVscy9yb3V0ZS50cz9iMmQ5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcidcbmltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcydcbmltcG9ydCB0eXBlIHsgRGF0YWJhc2UgfSBmcm9tICdAL3NoYXJlZC90eXBlcydcblxuY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQ8RGF0YWJhc2U+KFxuICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSFcbilcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKVxuXG4gICAgLy8gUGFyc2UgcXVlcnkgcGFyYW1ldGVyc1xuICAgIGNvbnN0IHBhZ2UgPSBwYXJzZUludChzZWFyY2hQYXJhbXMuZ2V0KCdwYWdlJykgfHwgJzEnKVxuICAgIGNvbnN0IGxpbWl0ID0gTWF0aC5taW4ocGFyc2VJbnQoc2VhcmNoUGFyYW1zLmdldCgnbGltaXQnKSB8fCAnMjAnKSwgMTAwKSAvLyBNYXggMTAwIGl0ZW1zIHBlciBwYWdlXG4gICAgY29uc3Qgb2Zmc2V0ID0gKHBhZ2UgLSAxKSAqIGxpbWl0XG5cbiAgICAvLyBGaWx0ZXIgcGFyYW1ldGVyc1xuICAgIGNvbnN0IG1ha2UgPSBzZWFyY2hQYXJhbXMuZ2V0KCdtYWtlJylcbiAgICBjb25zdCBib2R5VHlwZSA9IHNlYXJjaFBhcmFtcy5nZXQoJ2JvZHlfdHlwZScpXG4gICAgY29uc3QgcHJpY2VNaW4gPSBzZWFyY2hQYXJhbXMuZ2V0KCdwcmljZV9taW4nKVxuICAgIGNvbnN0IHByaWNlTWF4ID0gc2VhcmNoUGFyYW1zLmdldCgncHJpY2VfbWF4JylcbiAgICBjb25zdCByYW5nZU1pbiA9IHNlYXJjaFBhcmFtcy5nZXQoJ3JhbmdlX21pbicpXG4gICAgY29uc3QgcHJvZHVjdGlvblN0YXR1cyA9IHNlYXJjaFBhcmFtcy5nZXQoJ3Byb2R1Y3Rpb25fc3RhdHVzJykgfHwgJ2N1cnJlbnQnXG4gICAgY29uc3QgZmVhdHVyZWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCdmZWF0dXJlZCcpXG4gICAgY29uc3QgYmVzdFZhbHVlID0gc2VhcmNoUGFyYW1zLmdldCgnYmVzdF92YWx1ZScpXG5cbiAgICAvLyBTb3J0IHBhcmFtZXRlcnNcbiAgICBjb25zdCBzb3J0QnkgPSBzZWFyY2hQYXJhbXMuZ2V0KCdzb3J0X2J5JykgfHwgJ3BvcHVsYXJpdHlfc2NvcmUnXG4gICAgY29uc3Qgc29ydE9yZGVyID0gc2VhcmNoUGFyYW1zLmdldCgnc29ydF9vcmRlcicpIHx8ICdkZXNjJ1xuXG4gICAgLy8gU2VhcmNoIHBhcmFtZXRlclxuICAgIGNvbnN0IHNlYXJjaCA9IHNlYXJjaFBhcmFtcy5nZXQoJ3NlYXJjaCcpXG5cbiAgICAvLyBCdWlsZCBxdWVyeSB3aXRoIG1hbnVmYWN0dXJlciBqb2luXG4gICAgbGV0IHF1ZXJ5ID0gc3VwYWJhc2UuZnJvbSgnZXZfbW9kZWxzJykuc2VsZWN0KGBcbiAgICAgICAgaWQsXG4gICAgICAgIG1ha2UsXG4gICAgICAgIG1vZGVsLFxuICAgICAgICB5ZWFyLFxuICAgICAgICB0cmltLFxuICAgICAgICBib2R5X3R5cGUsXG4gICAgICAgIHByaWNlX21zcnAsXG4gICAgICAgIHByaWNlX2Jhc2UsXG4gICAgICAgIHByb2R1Y3Rpb25fc3RhdHVzLFxuICAgICAgICBiYXR0ZXJ5X2NhcGFjaXR5X2t3aCxcbiAgICAgICAgcmFuZ2VfZXBhX21pbGVzLFxuICAgICAgICByYW5nZV9yZWFsX3dvcmxkX21pbGVzLFxuICAgICAgICBlZmZpY2llbmN5X21wZ2UsXG4gICAgICAgIGNoYXJnaW5nX3NwZWVkX2RjX2t3LFxuICAgICAgICBjaGFyZ2luZ190aW1lXzEwXzgwX21pbnV0ZXMsXG4gICAgICAgIGFjY2VsZXJhdGlvbl8wXzYwX21waCxcbiAgICAgICAgc2VhdGluZ19jYXBhY2l0eSxcbiAgICAgICAgaW1hZ2VzLFxuICAgICAgICBpc19mZWF0dXJlZCxcbiAgICAgICAgcG9wdWxhcml0eV9zY29yZSxcbiAgICAgICAgZWRpdG9yX2Nob2ljZSxcbiAgICAgICAgYmVzdF92YWx1ZSxcbiAgICAgICAgZXZfbWFudWZhY3R1cmVycyFpbm5lcihcbiAgICAgICAgICBuYW1lLFxuICAgICAgICAgIGxvZ29fdXJsXG4gICAgICAgIClcbiAgICAgIGApXG5cbiAgICAvLyBBcHBseSBmaWx0ZXJzXG4gICAgaWYgKG1ha2UpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ21ha2UnLCBtYWtlKVxuICAgIH1cblxuICAgIGlmIChib2R5VHlwZSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5lcSgnYm9keV90eXBlJywgYm9keVR5cGUpXG4gICAgfVxuXG4gICAgaWYgKHByaWNlTWluKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lmd0ZSgncHJpY2VfbXNycCcsIHBhcnNlSW50KHByaWNlTWluKSlcbiAgICB9XG5cbiAgICBpZiAocHJpY2VNYXgpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkubHRlKCdwcmljZV9tc3JwJywgcGFyc2VJbnQocHJpY2VNYXgpKVxuICAgIH1cblxuICAgIGlmIChyYW5nZU1pbikge1xuICAgICAgcXVlcnkgPSBxdWVyeS5ndGUoJ3JhbmdlX2VwYV9taWxlcycsIHBhcnNlSW50KHJhbmdlTWluKSlcbiAgICB9XG5cbiAgICBpZiAocHJvZHVjdGlvblN0YXR1cykge1xuICAgICAgcXVlcnkgPSBxdWVyeS5lcSgncHJvZHVjdGlvbl9zdGF0dXMnLCBwcm9kdWN0aW9uU3RhdHVzKVxuICAgIH1cblxuICAgIGlmIChmZWF0dXJlZCA9PT0gJ3RydWUnKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdpc19mZWF0dXJlZCcsIHRydWUpXG4gICAgfVxuXG4gICAgaWYgKGJlc3RWYWx1ZSA9PT0gJ3RydWUnKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdiZXN0X3ZhbHVlJywgdHJ1ZSlcbiAgICB9XG5cbiAgICAvLyBBcHBseSBzZWFyY2hcbiAgICBpZiAoc2VhcmNoKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lm9yKGBtYWtlLmlsaWtlLiUke3NlYXJjaH0lLG1vZGVsLmlsaWtlLiUke3NlYXJjaH0lLHRyaW0uaWxpa2UuJSR7c2VhcmNofSVgKVxuICAgIH1cblxuICAgIC8vIEFwcGx5IHNvcnRpbmdcbiAgICBjb25zdCB2YWxpZFNvcnRGaWVsZHMgPSBbXG4gICAgICAncG9wdWxhcml0eV9zY29yZScsXG4gICAgICAncHJpY2VfbXNycCcsXG4gICAgICAncmFuZ2VfZXBhX21pbGVzJyxcbiAgICAgICd5ZWFyJyxcbiAgICAgICdtYWtlJyxcbiAgICAgICdtb2RlbCcsXG4gICAgXVxuICAgIGNvbnN0IHNvcnRGaWVsZCA9IHZhbGlkU29ydEZpZWxkcy5pbmNsdWRlcyhzb3J0QnkpID8gc29ydEJ5IDogJ3BvcHVsYXJpdHlfc2NvcmUnXG4gICAgY29uc3Qgb3JkZXIgPSBzb3J0T3JkZXIgPT09ICdhc2MnID8gJ2FzYycgOiAnZGVzYydcblxuICAgIHF1ZXJ5ID0gcXVlcnkub3JkZXIoc29ydEZpZWxkLCB7IGFzY2VuZGluZzogb3JkZXIgPT09ICdhc2MnIH0pXG5cbiAgICAvLyBBZGQgc2Vjb25kYXJ5IHNvcnQgZm9yIGNvbnNpc3RlbmN5XG4gICAgaWYgKHNvcnRGaWVsZCAhPT0gJ3BvcHVsYXJpdHlfc2NvcmUnKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5Lm9yZGVyKCdwb3B1bGFyaXR5X3Njb3JlJywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG4gICAgfVxuXG4gICAgLy8gQXBwbHkgcGFnaW5hdGlvblxuICAgIHF1ZXJ5ID0gcXVlcnkucmFuZ2Uob2Zmc2V0LCBvZmZzZXQgKyBsaW1pdCAtIDEpXG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yLCBjb3VudCB9ID0gYXdhaXQgcXVlcnlcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgRVYgbW9kZWxzOicsIGVycm9yKVxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggRVYgbW9kZWxzJyB9LCB7IHN0YXR1czogNTAwIH0pXG4gICAgfVxuXG4gICAgLy8gR2V0IHRvdGFsIGNvdW50IGZvciBwYWdpbmF0aW9uXG4gICAgY29uc3QgeyBjb3VudDogdG90YWxDb3VudCB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdldl9tb2RlbHMnKVxuICAgICAgLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSlcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBkYXRhLFxuICAgICAgcGFnaW5hdGlvbjoge1xuICAgICAgICBwYWdlLFxuICAgICAgICBsaW1pdCxcbiAgICAgICAgdG90YWw6IHRvdGFsQ291bnQgfHwgMCxcbiAgICAgICAgdG90YWxQYWdlczogTWF0aC5jZWlsKCh0b3RhbENvdW50IHx8IDApIC8gbGltaXQpLFxuICAgICAgICBoYXNOZXh0OiBvZmZzZXQgKyBsaW1pdCA8ICh0b3RhbENvdW50IHx8IDApLFxuICAgICAgICBoYXNQcmV2OiBwYWdlID4gMSxcbiAgICAgIH0sXG4gICAgICBmaWx0ZXJzOiB7XG4gICAgICAgIG1ha2UsXG4gICAgICAgIGJvZHlUeXBlLFxuICAgICAgICBwcmljZU1pbixcbiAgICAgICAgcHJpY2VNYXgsXG4gICAgICAgIHJhbmdlTWluLFxuICAgICAgICBwcm9kdWN0aW9uU3RhdHVzLFxuICAgICAgICBmZWF0dXJlZCxcbiAgICAgICAgYmVzdFZhbHVlLFxuICAgICAgICBzZWFyY2gsXG4gICAgICB9LFxuICAgICAgc29ydDoge1xuICAgICAgICBzb3J0Qnk6IHNvcnRGaWVsZCxcbiAgICAgICAgc29ydE9yZGVyOiBvcmRlcixcbiAgICAgIH0sXG4gICAgfSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdVbmV4cGVjdGVkIGVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyB9LCB7IHN0YXR1czogNTAwIH0pXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJjcmVhdGVDbGllbnQiLCJzdXBhYmFzZSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsIkdFVCIsInJlcXVlc3QiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJwYWdlIiwicGFyc2VJbnQiLCJnZXQiLCJsaW1pdCIsIk1hdGgiLCJtaW4iLCJvZmZzZXQiLCJtYWtlIiwiYm9keVR5cGUiLCJwcmljZU1pbiIsInByaWNlTWF4IiwicmFuZ2VNaW4iLCJwcm9kdWN0aW9uU3RhdHVzIiwiZmVhdHVyZWQiLCJiZXN0VmFsdWUiLCJzb3J0QnkiLCJzb3J0T3JkZXIiLCJzZWFyY2giLCJxdWVyeSIsImZyb20iLCJzZWxlY3QiLCJlcSIsImd0ZSIsImx0ZSIsIm9yIiwidmFsaWRTb3J0RmllbGRzIiwic29ydEZpZWxkIiwiaW5jbHVkZXMiLCJvcmRlciIsImFzY2VuZGluZyIsInJhbmdlIiwiZGF0YSIsImVycm9yIiwiY291bnQiLCJjb25zb2xlIiwianNvbiIsInN0YXR1cyIsInRvdGFsQ291bnQiLCJoZWFkIiwicGFnaW5hdGlvbiIsInRvdGFsIiwidG90YWxQYWdlcyIsImNlaWwiLCJoYXNOZXh0IiwiaGFzUHJldiIsImZpbHRlcnMiLCJzb3J0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ev-models/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fev-models%2Froute&page=%2Fapi%2Fev-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fev-models%2Froute.ts&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();