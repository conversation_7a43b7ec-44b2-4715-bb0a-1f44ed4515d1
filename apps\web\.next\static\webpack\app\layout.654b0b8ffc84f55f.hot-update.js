"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"3257a2f36df7\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NjM4MCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMyNTdhMmYzNmRmN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/comparison/ComparisonPanel.tsx":
/*!*******************************************************!*\
  !*** ./src/components/comparison/ComparisonPanel.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComparisonPanel: function() { return /* binding */ ComparisonPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,ExternalLink,Trash2,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/ComparisonContext */ \"(app-pages-browser)/./src/contexts/ComparisonContext.tsx\");\n/* harmony import */ var _hooks_useComparisonActions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useComparisonActions */ \"(app-pages-browser)/./src/hooks/useComparisonActions.ts\");\n/* harmony import */ var _shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/shared/utils/ev-buyer-guide */ \"(app-pages-browser)/../../packages/shared/src/utils/ev-buyer-guide.ts\");\n/* harmony import */ var _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/shared/constants/ev-buyer-guide */ \"(app-pages-browser)/../../packages/shared/src/constants/ev-buyer-guide.ts\");\n/* __next_internal_client_entry_do_not_use__ ComparisonPanel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ComparisonPanel() {\n    _s();\n    const { state, togglePanel, closePanel } = (0,_contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_8__.useComparison)();\n    const { removeFromComparison, clearComparison } = (0,_hooks_useComparisonActions__WEBPACK_IMPORTED_MODULE_9__.useComparisonActions)();\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (state.models.length === 0) {\n        return null;\n    }\n    const handleToggleMinimize = ()=>{\n        setIsMinimized(!isMinimized);\n    };\n    const handleCompareAll = ()=>{\n        // Navigate to comparison page with all models\n        const modelIds = state.models.map((m)=>m.id).join(\",\");\n        window.open(\"/compare?models=\".concat(modelIds), \"_blank\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)] transition-all duration-300\", state.isOpen ? \"translate-y-0\" : \"translate-y-full\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"border-electric-200 shadow-2xl dark:border-electric-800\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    className: \"pb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"flex items-center gap-2 text-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Compare, {\n                                        className: \"h-5 w-5 text-electric-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Compare EVs\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"ml-2\",\n                                        children: [\n                                            state.models.length,\n                                            \"/4\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleToggleMinimize,\n                                        className: \"h-8 w-8 p-0\",\n                                        children: isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: closePanel,\n                                        className: \"h-8 w-8 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-64 space-y-3 overflow-y-auto\",\n                            children: state.models.map((model)=>{\n                                var _model_images;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 rounded-lg border p-3 hover:bg-gray-50 dark:hover:bg-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-12 w-16 shrink-0 overflow-hidden rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: ((_model_images = model.images) === null || _model_images === void 0 ? void 0 : _model_images[0]) || _shared_constants_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_11__.PLACEHOLDER_IMAGES.ev_model,\n                                                alt: \"\".concat(model.make, \" \").concat(model.model),\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-0 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"truncate text-sm font-medium\",\n                                                    children: [\n                                                        model.make,\n                                                        \" \",\n                                                        model.model\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: [\n                                                        (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_10__.formatPrice)(model.price_msrp),\n                                                        \" • \",\n                                                        (0,_shared_utils_ev_buyer_guide__WEBPACK_IMPORTED_MODULE_10__.formatRange)(model.range_epa_miles)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    className: \"h-8 w-8 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/ev-models/\".concat(model.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>removeFromComparison(model.id),\n                                                    className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, model.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 border-t pt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleCompareAll,\n                                    className: \"flex-1\",\n                                    disabled: state.models.length < 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Compare, {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Compare All\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: clearComparison,\n                                    className: \"text-red-600 hover:text-red-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_ExternalLink_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this),\n                        state.models.length < 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-xs text-gray-600 dark:text-gray-400\",\n                            children: \"Add at least 2 models to start comparing\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\comparison\\\\ComparisonPanel.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(ComparisonPanel, \"P4O5Fpyx5RmVhHbTYYyO2XYirf8=\", false, function() {\n    return [\n        _contexts_ComparisonContext__WEBPACK_IMPORTED_MODULE_8__.useComparison,\n        _hooks_useComparisonActions__WEBPACK_IMPORTED_MODULE_9__.useComparisonActions\n    ];\n});\n_c = ComparisonPanel;\nvar _c;\n$RefreshReg$(_c, \"ComparisonPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/comparison/ComparisonPanel.tsx\n"));

/***/ })

});